using CommonLib;
using System;
using System.Collections.Generic;
using System.Data;

namespace Account.Web
{
    public class PayHelper
    {
        public static bool CreateOrder(PayOrderEntity code)
        {
            string strSQL = string.Format(@"insert into pay_order(price, type, reallyPrice, remark, param, orderId, createDate, payDate,closeDate, returnUrl, state, isAuto, payUrl,payId,source,ticket360Id) values ('{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}','{10}','{11}','{12}','{13}','{14}','{15}')"
                , code.price.ToString("F2")
                , code.type
                , code.reallyPrice.ToString("F2")
                , code.remark
                , code.param
                , code.orderId
                , code.createDate
                , code.payDate ?? ""
                , code.closeDate ?? ""
                , code.returnUrl
                , code.state
                , code.isAuto
                , code.payUrl
                , code.payId
                , code.from.GetHashCode()
                , code.ticket360Id ?? ""
                );
            return CommonHelper.DBHelper.Ado.ExecuteNonQuery(strSQL) > 0;
        }

        public static bool UpdateOrder(PayOrderEntity code)
        {
            string strSQL = string.Format(@"update pay_order set type = '{0}', state = '{1}', payDate = '{2}',closeDate = '{3}' where orderId = '{4}'"
                , code.type
                , code.state
                , code.payDate ?? ""
                , code.closeDate ?? ""
                , code.orderId
                );
            return CommonHelper.DBHelper.Ado.ExecuteNonQuery(strSQL) > 0;
        }

        public static bool setTimeout()
        {
            string strSQL = string.Format(@"update pay_order set state='-1' where state = '0' and closeDate < '{0}'"
            , ServerTime.DateTime.ToString("yyyy-MM-dd HH:mm:ss"));
            var result = CommonHelper.DBHelper.Ado.ExecuteNonQuery(strSQL) > 0;
            return result;
        }

        public static bool setState(string id, int state)
        {
            //appCode,machine,NType,dtReg,dtExpired,MaxWindow,MaxLogin,IsForbid
            string strSQL = string.Format(@"update pay_order set state='{1}' where orderId = '{0}'"
            , id, state);
            var result = CommonHelper.DBHelper.Ado.ExecuteNonQuery(strSQL) > 0;
            return result;
        }

        public static bool UpdatePayType(string orderId, int type)
        {
            //appCode,machine,NType,dtReg,dtExpired,MaxWindow,MaxLogin,IsForbid
            string strSQL = string.Format(@"update pay_order set type='{1}' where orderId = '{0}'"
            , orderId, type);
            var result = CommonHelper.DBHelper.Ado.ExecuteNonQuery(strSQL) > 0;
            return result;
        }

        public static bool DelByOrder(string orderId)
        {
            var strSql = string.Format(@"delete from pay_order where orderId='{0}'", orderId);
            return CommonHelper.DBHelper.Ado.ExecuteNonQuery(strSql) > 0;
        }

        public static bool DelByState()
        {
            var strSql = @"delete from pay_order where state='-1'";
            return CommonHelper.DBHelper.Ado.ExecuteNonQuery(strSql) > 0;
        }

        public static PayOrderEntity getByOrderId(string orderId)
        {
            PayOrderEntity food = null;
            string strSQL = @"select * from pay_order where orderId = '{0}'";
            DataSet result = CommonHelper.DBHelper.Ado.ExecuteDataSet(string.Format(strSQL, orderId));
            if (!(result == null || result.Tables.Count < 1 || result.Tables[0].Rows.Count <= 0))
            {
                food = GetPayOrder(result.Tables[0].Rows[0]);
            }
            return food;
        }

        public static List<PayOrderEntity> findOrderByRemarkAndParamAndPrice(string remark, string param, double price)
        {
            var food = new List<PayOrderEntity>();
            string strSQL = @"select * from pay_order where remark = '{0}' and param = '{1}' and price = '{2}' and state=0";
            DataSet result = CommonHelper.DBHelper.Ado.ExecuteDataSet(string.Format(strSQL, remark, param, price.ToString("F2")));
            if (!(result == null || result.Tables.Count < 1 || result.Tables[0].Rows.Count <= 0))
            {
                foreach (DataRow item in result.Tables[0].Rows)
                {
                    food.Add(GetPayOrder(item));
                }
            }
            return food;
        }

        public static int findByReallyPriceAndType(double reallyPrice)
        {
            string strSQL = @"select count(*) from pay_order where reallyPrice = '{0}' and state = 0";
            var result = int.Parse(CommonHelper.DBHelper.Ado.ExecuteScalar(string.Format(strSQL, reallyPrice.ToString("F2"))).ToString());
            return result;
        }

        public static PayOrderEntity findByOrderId(string orderId)
        {
            PayOrderEntity food = null;
            string strSQL = @"select * from pay_order where orderId = '{0}'";
            DataSet result = CommonHelper.DBHelper.Ado.ExecuteDataSet(string.Format(strSQL, orderId));
            if (!(result == null || result.Tables.Count < 1 || result.Tables[0].Rows.Count <= 0))
            {
                food = GetPayOrder(result.Tables[0].Rows[0]);
            }
            return food;
        }

        public static PayOrderEntity findByPayId(string payId)
        {
            PayOrderEntity food = null;
            string strSQL = @"select * from pay_order where payId = '{0}'";
            DataSet result = CommonHelper.DBHelper.Ado.ExecuteDataSet(string.Format(strSQL, payId));
            if (!(result == null || result.Tables.Count < 1 || result.Tables[0].Rows.Count <= 0))
            {
                food = GetPayOrder(result.Tables[0].Rows[0]);
            }
            return food;
        }



        public static PayOrderEntity findByPayDate(DateTime payDate, int type)
        {
            PayOrderEntity food = null;
            string strSQL = @"select * from pay_order where payDate = '{0}' and type = '{1}'";
            DataSet result = CommonHelper.DBHelper.Ado.ExecuteDataSet(string.Format(strSQL, payDate.ToString("yyyy-MM-dd HH:mm:ss"), type));
            if (!(result == null || result.Tables.Count < 1 || result.Tables[0].Rows.Count <= 0))
            {
                food = GetPayOrder(result.Tables[0].Rows[0]);
            }
            return food;
        }

        public static PayOrderEntity findByReallyPriceAndState(double reallyPrice, int state)
        {
            PayOrderEntity food = null;
            string strSQL = @"select * from pay_order where reallyPrice = '{0}' and state = '{1}'";
            DataSet result = CommonHelper.DBHelper.Ado.ExecuteDataSet(string.Format(strSQL, reallyPrice.ToString("F2"), state));
            if (!(result == null || result.Tables.Count < 1 || result.Tables[0].Rows.Count <= 0))
            {
                food = GetPayOrder(result.Tables[0].Rows[0]);
            }
            return food;
        }

        public static List<PayOrderEntity> findByReallyPriceAndStateAndTime(double reallyPrice, int state, DateTime startDate, DateTime endDate)
        {
            var food = new List<PayOrderEntity>();
            string strSQL = @"select * from pay_order where reallyPrice = '{0}' and state = '{1}' and closeDate between '{2}' and '{3}' order by createDate desc";
            DataSet result = CommonHelper.DBHelper.Ado.ExecuteDataSet(string.Format(strSQL, reallyPrice.ToString("F2"), state, startDate.ToString("yyyy-MM-dd HH:mm:ss"), endDate.ToString("yyyy-MM-dd HH:mm:ss")));
            if (!(result == null || result.Tables.Count < 1 || result.Tables[0].Rows.Count <= 0))
            {
                foreach (DataRow item in result.Tables[0].Rows)
                {
                    food.Add(GetPayOrder(item));
                }
            }
            return food;
        }

        public static DataTable GetPayList(string strStart, string strAppCode)
        {
            string strSQL = @"select orderId,payId,price,reallyPrice,case type when 0 then '未选择' when 1 then '微信' else '支付宝' end as type,case state when -1 then '已过期' when 0 then '待支付' when 1 then '支付成功' else '待处理' end as state,param,remark,createDate,payDate,closeDate,case source when 0 then '助手' when 1 then '360' else '其他' end as source,ticket360Id,'' as '操作' from pay_order where {0} order by createDate desc";
            if (!string.IsNullOrEmpty(strAppCode))
            {
                strSQL = string.Format(strSQL, "param='" + strAppCode.ToLower() + "'");
            }
            else
            {
                if (!string.IsNullOrEmpty(strStart))
                {
                    strSQL = string.Format(strSQL, "createDate>='" + strStart + "'");
                }
                else
                    strSQL = string.Format(strSQL, "1=1");
            }
            return CommonHelper.DBHelper.Ado.ExecuteDataTable(strSQL);
        }

        public static PayOrderEntity GetPayOrder(DataRow row)
        {
            PayOrderEntity CodeEntity = new PayOrderEntity
            {
                orderId = row["orderId"].ToString(),
                payId = row["payId"].ToString(),
                price = Math.Round(BoxUtil.GetDoubleFromObject(row["price"].ToString()), 2),
                reallyPrice = Math.Round(BoxUtil.GetDoubleFromObject(row["reallyPrice"].ToString()), 2),
                type = BoxUtil.GetInt32FromObject(row["type"].ToString()),
                param = row["param"].ToString(),
                createDate = row["createDate"].ToString(),
                payDate = row["payDate"].ToString(),
                closeDate = row["closeDate"].ToString(),
                isAuto = BoxUtil.GetInt32FromObject(row["isAuto"].ToString()),
                returnUrl = row["returnUrl"].ToString(),
                payUrl = row["payUrl"].ToString(),
                remark = row["remark"].ToString(),
                state = BoxUtil.GetInt32FromObject(row["state"].ToString()),
                from = (OrderFrom)BoxUtil.GetInt32FromObject(row["source"].ToString()),
                ticket360Id = row["ticket360Id"]?.ToString() ?? ""
            };
            return CodeEntity;
        }
    }
}