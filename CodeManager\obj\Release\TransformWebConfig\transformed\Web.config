﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="UserChargeTypeSetting" type="CommonLib.UserConfig.ChargeTypeConfigurationSectionHandler, CommonLib" />
    <section name="ObjectItemTypeSetting" type="CommonLib.UserConfig.ObjectItemConfigurationSectionHandler, CommonLib" />
    <section name="TipMsgSetting" type="CommonLib.UserConfig.TipMsgConfigurationSectionHandler, CommonLib" />
  </configSections>
  <appSettings>
    <add key="pwd" value="123" />
    <add key="strEmailAccount" value="<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;" />
    <add key="strEmailPWD" value="Aa@qpzs01" />
    <add key="StrRelpace" value="订单,|订,言丁 " />
    <add key="Encrypt" value="!(*_^%$#" />
    <add key="IsEncrypt" value="true" />
    <add key="DBFilePath" value="D:\web\data\" />
    <add key="IsRegToGeRen" value="False" />
    <add key="IsRegToProfessional" value="True" />
    <add key="NRegSendDays" value="7" />
    <add key="FileHostUrl" value="http://**************:7070/" />
    <add key="FilePath" value="C:\Image\" />
    <!--打码限制频率（毫秒）-->
    <add key="NBlackMSencond" value="1000" />
    <!--单位时间内最多打多少码-->
    <add key="NMaxExecPerSecond" value="200" />
    <!--打码超过频率黑名单秒数-->
    <add key="NMaxExecBlackSecond" value="3" />
    <!--是否开启打码余额限制-->
    <add key="IsValidateCodeAmount" value="False" />
    <!--打码超时时间（秒）-->
    <add key="NGetCodeTimeOut" value="10" />
    <add key="OcrGroupConfig" value="0,不限|1,百度|4,有道|5,搜狗|6,讯飞|12,合合|7,迅捷|8,VIVO|10,学而思|11,汉王|13,字节" />
    <add key="TipMsg" value="专业版/企业版限时特惠，感谢您的支持！" />
    <add key="DefaultSiteHost" value="ocr.qq.com" />
    <add key="IsCanViewFile" value="true" />
    <add key="MinVersionDate" value="2024-04-26" />
    <add key="MinVersionStr" value="当前版本过低，请升级到最新版！\n可通过以下方式升级：\n①菜单-&gt;检查更新\n②到官网下载安装(https://ocr.oldfish.cn)" />
    <add key="NMaxUserCount" value="3" />
    <add key="IsLogRequest" value="false" />
    <add key="PayToken" value="d59050bf518a6ae3e2ba9ecc57f75e14" />
    <add key="PayTimeOutMinute" value="10" />
    <add key="PayQQKeFu" value="365833440" />
    <add key="PayReturnUrl" value="http://localhost:19225/Pay.aspx" />
    <add key="IsPayPriceAdd" value="false" />
    <add key="PayMaxRandom" value="0.1" />
    <add key="PayZFBDTM" value="2088602303746560" />
    <add key="PayZFBJTM" value="https://qr.alipay.com/fkx181461kkbgljdikdzs37" />
    <add key="PayWXJTM" value="wxp://f2f0dnvH8ZD0EuP0ks8PudVND9P1aK3WpToP6GLKWsxqz-M" />
    <!--<add key="StrOuterStaticUrl" value="https://cdn.oldfish.cn/" />-->
    <add key="StrOuterStaticUrl" value="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/" />
    <add key="StrInnerStaticUrl" value="https://scm-file-new-1301864755.yijiupicdn.com/f0481b1f-3226-415a-bcc4-9b28e2fb0f9f/" />
    <!--<add key="StrOuterCDNUrl" value="https://cdn.oldfish.cn/" />-->
    <add key="StrOuterCDNUrl" value="https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com/cdn/" />
    <add key="StrInnerCDNUrl" value="https://scm-file-new-1301864755.yijiupicdn.com/cdn/" />
    <!-- 360支付配置 -->
    <add key="Pay360QId" value="51304670" />
    <add key="Pay360AppId" value="1bL0mcN" />
    <add key="Pay360AppSecret" value="79ac665dcf96c16448c3934682287577" />
    <!-- 新翻译系统配置 -->
    <!-- 是否启用新翻译系统 -->
    <add key="EnableNewTranslationSystem" value="false" />
    <!-- 最大并发翻译任务数 -->
    <add key="MaxConcurrentTranslations" value="3" />
    <!-- 翻译超时时间（秒） -->
    <add key="TranslationTimeoutSeconds" value="30" />
    <!-- 队列处理间隔（秒） -->
    <add key="QueueProcessIntervalSeconds" value="10" />
    <!-- 默认翻译引擎 -->
    <add key="DefaultTranslationEngine" value="Edge" />
    <!-- 是否启用翻译引擎故障转移 -->
    <add key="EnableEngineFailover" value="true" />
    <!-- 翻译引擎优先级列表（逗号分隔，Edge优先级最高） -->
    <add key="TranslationEnginePriority" value="Edge,Microsoft,Google,Baidu" />
    <!-- 翻译缓存过期时间（小时） -->
    <add key="TranslationCacheExpirationHours" value="24" />
    <!-- Microsoft Translator 配置 -->
    <add key="MicrosoftTranslatorApiKey" value="" />
    <add key="MicrosoftTranslatorRegion" value="global" />
    <!-- Google Translate 配置 -->
    <add key="GoogleTranslateApiKey" value="" />
    <!-- 百度翻译配置 -->
    <add key="BaiduTranslateAppId" value="" />
    <add key="BaiduTranslateSecretKey" value="" />
  </appSettings>
  <ObjectItemTypeSetting>
    <LstSettings>
      <ObjectTypeSetting Name="ServerGroup">
        <LstItem>
          <ObjectTypeItem Name="不限" Code="0" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="百度" Code="1" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="腾讯" Code="2" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="阿里" Code="3" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="字节" Code="13" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="有道" Code="4" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="搜狗" Code="5" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="讯飞" Code="6" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="合合" Code="12" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="迅捷" Code="7" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="VIVO" Code="8" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="金山" Code="9" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="false" />
          <ObjectTypeItem Name="学而思" Code="10" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="汉王" Code="11" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="MathPix" Code="15" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
          <ObjectTypeItem Name="AWS" Code="16" Desc="" Remark="" DescUrl="" UpdateUrl="" Enable="true" />
        </LstItem>
      </ObjectTypeSetting>
      <ObjectTypeSetting Name="LocalGroup">
        <LstItem>
          <ObjectTypeItem Name="飞浆Mobile" Code="90001" Desc="百度旗下-飞桨开源的移动版文字识别模型套件。" Remark="" DescUrl="https://www.paddlepaddle.org.cn/hub/scene/ocr" UpdateUrl="https://scm-file-new-1301864755.yijiupicdn.com/cdn/update/%E9%A3%9E%E6%B5%86Mobile.zip" />
          <ObjectTypeItem Name="中文识别Lite" Code="90003" Desc="超轻量级中文文字识别套件，支持ncnn、mnn、tnn推理(DbNet + Crnn + AngleNet)" Remark="" DescUrl="https://github.com/DayBreak-u/chineseocr_lite" UpdateUrl="https://scm-file-new-1301864755.yijiupicdn.com/cdn/update/%E4%B8%AD%E6%96%87%E8%AF%86%E5%88%ABLite.zip" />
          <ObjectTypeItem Name="Win10 OCR" Code="90004" Desc="Win10及以上系统自带的OCR功能，针对标准印刷体，识别速度很快。" Remark="" DescUrl="https://learn.microsoft.com/zh-cn/uwp/api/windows.media.ocr?view=winrt-22621" NeedInstall="false" />
        </LstItem>
      </ObjectTypeSetting>
      <ObjectTypeSetting Name="LocalImage">
        <LstItem>
          <ObjectTypeItem Name="图片增强" Code="51" />
          <ObjectTypeItem Name="去屏幕纹" Code="52" />
          <ObjectTypeItem Name="老照片修复" Code="53" />
        </LstItem>
      </ObjectTypeSetting>
    </LstSettings>
  </ObjectItemTypeSetting>
  <UserChargeTypeSetting>
    <UserTypeSettings>
      <UserTypeSetting UserType="个人版" PriceType="按年" PerPrice="100" Enable="true">
        <ChargeTypes>
          <ChargeType Name="终身" IncreaseStep="1" Enable="true" IsDefault="true" Tag="_new" Discount="0.68" Desc="" LimitActivity="true" LimitDiscount="0.36" LimitDesc="" DtEnd="2025-12-31" />
        </ChargeTypes>
      </UserTypeSetting>
      <UserTypeSetting UserType="专业版" PriceType="按年" PerPrice="100" Enable="true">
        <ChargeTypes>
          <ChargeType Name="一年" IncreaseStep="1" Enable="true" IsDefault="false" Tag="hot" Discount="0.60" Desc="" LimitActivity="true" LimitDiscount="0.58" LimitDesc="送终身个人版" DtEnd="2025-12-31" />
          <ChargeType Name="三年" IncreaseStep="1" Enable="true" IsDefault="true" Tag="recommond" Discount="1.80" Desc="" LimitActivity="true" LimitDiscount="1.38" LimitDesc="送终身个人版" DtEnd="2025-12-31" />
          <ChargeType Name="五年" IncreaseStep="1" Enable="true" IsDefault="false" Tag="_new" Discount="3.00" Desc="" LimitActivity="true" LimitDiscount="2.38" LimitDesc="送终身个人版" DtEnd="2025-12-31" />
        </ChargeTypes>
      </UserTypeSetting>
      <UserTypeSetting UserType="企业版" PriceType="按年" PerPrice="100" Enable="false">
        <ChargeTypes>
          <ChargeType Name="一年" IncreaseStep="1" Enable="true" IsDefault="false" Tag="hot" Discount="1.20" Desc="" LimitActivity="true" LimitDiscount="1.08" LimitDesc="送终身个人版" DtEnd="2025-12-31" />
          <ChargeType Name="三年" IncreaseStep="1" Enable="true" IsDefault="true" Tag="recommond" Discount="3.60" Desc="" LimitActivity="true" LimitDiscount="2.58" LimitDesc="送终身个人版" DtEnd="2025-12-31" />
          <ChargeType Name="终身" IncreaseStep="1" Enable="true" IsDefault="false" Tag="_new" Discount="6.00" Desc="" LimitActivity="true" LimitDiscount="4.98" LimitDesc="" DtEnd="2025-12-31" />
        </ChargeTypes>
      </UserTypeSetting>
      <UserTypeSetting UserType="旗舰版" PriceType="按年" PerPrice="100" Enable="true">
        <ChargeTypes>
          <ChargeType Name="一年" IncreaseStep="1" Enable="true" IsDefault="true" Tag="recommond" Discount="1.80" Desc="" LimitActivity="true" LimitDiscount="1.58" LimitDesc="送终身个人版" DtEnd="2025-12-31" />
          <ChargeType Name="三年" IncreaseStep="1" Enable="true" IsDefault="false" Tag="hot" Discount="5.40" Desc="" LimitActivity="true" LimitDiscount="3.68" LimitDesc="送终身个人版" DtEnd="2025-12-31" />
          <ChargeType Name="五年" IncreaseStep="1" Enable="true" IsDefault="false" Tag="_new" Discount="9.00" Desc="" LimitActivity="true" LimitDiscount="5.68" LimitDesc="送终身个人版" DtEnd="2025-12-31" />
        </ChargeTypes>
      </UserTypeSetting>
      <UserTypeSetting UserType="宇宙版" PriceType="按年" PerPrice="100" Enable="false">
        <ChargeTypes>
          <ChargeType Name="一年" IncreaseStep="1" Enable="true" IsDefault="true" Tag="recommond" Discount="2.40" Desc="" LimitActivity="true" LimitDiscount="2.08" LimitDesc="送终身个人版" DtEnd="2025-12-31" />
          <ChargeType Name="三年" IncreaseStep="1" Enable="true" IsDefault="false" Tag="hot" Discount="7.20" Desc="" LimitActivity="true" LimitDiscount="5.08" LimitDesc="送终身个人版" DtEnd="2025-12-31" />
          <ChargeType Name="终身" IncreaseStep="1" Enable="true" IsDefault="false" Tag="_new" Discount="12.00" Desc="" LimitActivity="true" LimitDiscount="8.98" LimitDesc="" DtEnd="2025-12-31" />
        </ChargeTypes>
      </UserTypeSetting>
      <UserTypeSetting UserType="API" PriceType="按年" PerPrice="100" Enable="false">
        <ChargeTypes>
          <ChargeType Name="10次请求" IncreaseStep="1" Enable="true" IsDefault="true" Tag="recommond" Discount="0.50" Desc="" LimitActivity="true" LimitDiscount="0.0001" LimitDesc="单次约0.001元" DtEnd="2025-12-31" />
          <ChargeType Name="1000次请求" IncreaseStep="1" Enable="true" IsDefault="true" Tag="recommond" Discount="0.50" Desc="" LimitActivity="true" LimitDiscount="0.49" LimitDesc="单次约0.049元" DtEnd="2025-12-31" />
          <ChargeType Name="3000次请求" IncreaseStep="1" Enable="true" IsDefault="false" Tag="hot" Discount="1.50" Desc="" LimitActivity="true" LimitDiscount="1.29" LimitDesc="单次约0.043元" DtEnd="2025-12-31" />
          <ChargeType Name="5000次请求" IncreaseStep="1" Enable="true" IsDefault="false" Tag="_new" Discount="2.50" Desc="" LimitActivity="true" LimitDiscount="1.89" LimitDesc="单次约0.037元" DtEnd="2025-12-31" />
        </ChargeTypes>
      </UserTypeSetting>
    </UserTypeSettings>
  </UserChargeTypeSetting>
  <TipMsgSetting>
    <LstMsg>
      <UserMsgEntity Id="ExpTipMsg" IsTipMsg="true" Title="尊敬的用户" Content="您的【[版本]】[天数]即将到期，\n诚挚地希望您能继续选择和支持我们！\n\n我们非常重视您的体验，也希望助手\n能够进一步提升您的工作效率！\n现在续费，您可以享受[专属VIP折扣]！\n\n我们将不断提升产品和服务质量，\n再次感谢您的支持！\n\nOCR助手服务团队\n　" ShowSecond="1800" IntervalHour="24" FontSize="13" ShowLevel="|-1|0|1|"></UserMsgEntity>
      <UserMsgEntity Id="RegTipMsg" IsTipMsg="true" Title="感谢您选择" Content="助手致力于为您提供更高效更便捷的生产力工具，以解放双手，提升您的工作效率！\n\n我们诚挚地邀请您注册为正式会员，\n活动期注册即送体验会员，可畅享专业版所有的高级功能！\n\n感谢您的选择，期待为您提供更优质的服务！\n\nOCR助手服务团队\n　" ShowSecond="1800" IntervalHour="24" FontSize="13" ShowLevel=""></UserMsgEntity>
      <UserMsgEntity Id="UpgradeMsg" IsTipMsg="true" Title="感谢您选择" Content="助手致力于为您提供更高效更便捷的生产力工具，以解放双手，提升您的工作效率！\n\n我们诚挚地邀请您升级到更强大的正式版，\n体验更完善的功能，享受更优质的服务！\n\n感谢您的选择，期待为您提供更优质的服务！\n\nOCR助手服务团队\n　" ShowSecond="1800" IntervalHour="24" FontSize="13" ShowLevel=""></UserMsgEntity>
    </LstMsg>
  </TipMsgSetting>
  <connectionStrings />
  <!--
    有关 web.config 更改的说明，请参见 http://go.microsoft.com/fwlink/?LinkId=235367。

    可在 <httpRuntime> 标记上设置以下特性。
      <system.Web>
        <httpRuntime targetFramework="4.7.2" />
      </system.Web>
  -->
  <system.web>
    <httpRuntime targetFramework="4.5" maxRequestLength="102400" />
    <pages enableSessionState="true" enableViewState="true" enableViewStateMac="false" controlRenderingCompatibilityVersion="4.0" clientIDMode="AutoID" />
    <customErrors mode="Off" />
    <compilation targetFramework="4.7.2">
      <assemblies>
        <add assembly="System.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
      </assemblies>
    </compilation>
    <authentication mode="Windows" />
  </system.web>
  <system.webServer>
    <modules>
      <add name="LanguagePathModule" type="Account.Web.LanguagePathModule" />
    </modules>
    <handlers>
      <add name="SignalR" path="signalr/*" verb="*" type="System.Web.Routing.UrlRoutingHandler, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
      <add name="HTMLHandler" path="*.html" verb="*" type="System.Web.StaticFileHandler" resourceType="File" />
    </handlers>
    <staticContent>
      <remove fileExtension=".dat" />
      <mimeMap fileExtension=".dat" mimeType="application/octet-stream" />
      <mimeMap fileExtension=".webp" mimeType="image/webp" />
    </staticContent>
  </system.webServer>
  <runtime>
    <gcServer enabled="true" />
    <gcConcurrent enabled="true" />
    <ThreadPool minWorkerThreads="100" minCompletionPortThreads="100" />
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.6.8.0" newVersion="2.6.8.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.6.8.0" newVersion="2.6.8.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.2.15.0" newVersion="1.2.15.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="FreeSql" publicKeyToken="a33928e5d4a4b39c" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.2.666.0" newVersion="3.2.666.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Data.SQLite" publicKeyToken="db937bc2d44ff139" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.0.116.0" newVersion="1.0.116.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
<!--ProjectGuid: F74FCA00-AE7C-45EB-8BFB-55D7B2BB5DB4-->