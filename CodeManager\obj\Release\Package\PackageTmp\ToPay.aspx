﻿<%@ Page Title="" Language="C#" CodeBehind="ToPay.aspx.cs" Inherits="Account.Web.ToPay" %>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head>
<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1"/>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="apple-mobile-web-app-capable" content="no"/>
<meta name="format-detection" content="telephone=no,email=no"/>
<meta name="renderer" content="webkit"/>
<meta http-equiv="Cache-control" content="no-cache">
<meta name="viewport" content="width=device-width,user-scalable=no,initial-scale=1,maximum-scale=1,minimum-scale=1">
<title>安全支付 | OCR文字识别助手 - VIP会员购买</title>
<meta name="description" content="OCR文字识别助手安全支付页面，支持微信、支付宝等多种支付方式，购买VIP会员享受更多专业功能：批量处理、离线识别、API接口等。" />
<meta name="keywords" content="OCR支付,VIP购买,会员升级,安全支付,微信支付,支付宝支付,扫码支付" />
<meta name="robots" content="noindex, nofollow" />

<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "PaymentMethod",
    "name": "OCR文字识别助手支付方式",
    "description": "支持微信支付、支付宝等多种安全支付方式",
    "paymentMethodId": "OCR_VIP_Payment",
    "acceptedPaymentMethod": [
        {
            "@type": "PaymentMethod",
            "name": "微信支付",
            "identifier": "WeChat Pay"
        },
        {
            "@type": "PaymentMethod",
            "name": "支付宝",
            "identifier": "Alipay"
        }
    ]
}
</script>
<style>
*{margin:0;padding:0;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}
html{font-size:14px;font-family:'Microsoft YaHei','SimHei','SimSun',Arial,sans-serif}
body{background:#f5f7fa;min-height:100vh;line-height:1.5;color:#333;padding:8px;margin:0}
.body{max-width:640px;min-width:300px;width:100%;margin:0 auto;background:#fff;border-radius:12px;box-shadow:0 4px 20px rgba(0,0,0,0.08);filter:progid:DXImageTransform.Microsoft.Shadow(color='#cccccc', Direction=135, Strength=3);overflow:hidden;position:relative;border:1px solid #e5e7eb}
.card{background:#fff;border-radius:8px;border:1px solid #e5e7eb;box-shadow:0 1px 3px rgba(0,0,0,0.05);filter:progid:DXImageTransform.Microsoft.Shadow(color='#eeeeee', Direction=135, Strength=1)}
.btn{padding:12px 16px;border:1px solid #e5e7eb;border-radius:8px;cursor:pointer;background:#fff;font-size:14px;text-decoration:none;color:#6b7280;text-align:center;display:inline-block;zoom:1}
.btn:hover{background:#f8fafc;border-color:#3b82f6;color:#3b82f6}
.mod-title{background:#f8fafc;background:-ms-linear-gradient(135deg,#f8fafc 0%,#fff 100%);background:linear-gradient(135deg,#f8fafc 0%,#fff 100%);filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#f8fafc', endColorstr='#ffffff', GradientType=1);padding:16px 20px 12px;text-align:center;border-bottom:1px solid #e5e7eb;position:relative}
.mod-title-border{position:absolute;top:0;left:0;right:0;height:3px;background:#3b82f6;background:-ms-linear-gradient(left,#3b82f6 0%,#8b5cf6 50%,#06b6d4 100%);filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#3b82f6', endColorstr='#06b6d4', GradientType=1)}
.mod-ct{background:#fff;text-align:center;color:#333;position:relative;border-radius:12px 12px 0 0}
.order{font-size:16px;font-weight:600;padding:12px 16px 8px;color:#333;display:flex;align-items:center;justify-content:center;gap:8px}
.product-icon{width:35px;height:35px;border-radius:6px;object-fit:cover}
.amount{font-size:32px;font-weight:700;margin:8px 0 16px;color:#1890ff;font-family:-apple-system,BlinkMacSystemFont,sans-serif}
.detail{margin-top:12px}
.detail-ct{display:none;font-size:13px;text-align:left;line-height:1.6;padding:16px;margin:0;overflow:hidden}
.detail-item{display:flex;align-items:center;padding:10px 0;border-bottom:1px solid #f3f4f6}
.detail-item:last-child{border-bottom:none}
.detail-label{color:#6b7280;font-weight:500;font-size:12px;width:80px;flex-shrink:0}
.detail-value{color:#111827;font-weight:600;flex:1;word-break:break-all;margin-left:12px}
.detail-value b{color:#1f2937;font-weight:700}
.detail-item:first-child .detail-value b{color:#dc2626;font-size:15px}
.detail-item:nth-child(2) .detail-value b,.detail-item:nth-child(3) .detail-value b{font-family:'Courier New',monospace;font-size:12px}
.detail-item:nth-child(4) .detail-value b{color:#6b7280;font-weight:600}
.arrow{margin:8px 0;padding:8px 12px;height:50px;margin-bottom:20px;border:1px solid #e5e7eb;border-radius:6px;cursor:pointer;background:#fff;transition:all .3s ease;display:flex;align-items:center;justify-content:center;gap:6px;font-size:13px;text-decoration:none;color:black}
.arrow:hover{background:#f8fafc;border-color:#3b82f6;color:#3b82f6;text-decoration:none}
.ico-arrow{display:inline-block;width:10px;height:10px;border:solid currentColor;border-width:0 2px 2px 0;transform:rotate(45deg);transition:all .3s ease;margin-left:2px}
.detail-open .detail-ct{display:block;animation:slideDown .3s ease-out}
.detail-open .ico-arrow{transform:rotate(225deg)}
@keyframes slideDown{from{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}

.pay_ul2{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;gap:16px;text-align:center;margin:12px 0 16px;padding:0 16px;zoom:1;list-style:none}
.no-flexbox .pay_ul2{display:block}
.pay_ul2:after{content:"";display:table;clear:both}
.pay_ul2 li{border-radius:12px;width:160px;height:60px;border:2px solid #e5e7eb;position:relative;text-align:center;cursor:pointer;background:#fff;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;zoom:1;vertical-align:top;margin:0 8px;line-height:80px;box-shadow:0 2px 8px rgba(0,0,0,0.08);filter:progid:DXImageTransform.Microsoft.Shadow(color='#dddddd', Direction=135, Strength=2);-webkit-transition:all 0.3s ease;-moz-transition:all 0.3s ease;-ms-transition:all 0.3s ease;-o-transition:all 0.3s ease;transition:all 0.3s ease}
.no-flexbox .pay_ul2 li{display:inline-block}
.pay_ul2 li:hover{border-color:#3b82f6;background:#f8fafc;box-shadow:0 8px 20px rgba(59,130,246,0.2);filter:progid:DXImageTransform.Microsoft.Shadow(color='#3b82f6', Direction=135, Strength=3)}
.pay_ul2 li img{object-fit:contain;margin:0 auto;display:inline-block;vertical-align:middle;line-height:normal;-webkit-transition:transform 0.3s ease;-moz-transition:transform 0.3s ease;-ms-transition:transform 0.3s ease;-o-transition:transform 0.3s ease;transition:transform 0.3s ease}
.pay_ul2 li:hover img{-webkit-transform:scale(1.1);-moz-transform:scale(1.1);-ms-transform:scale(1.1);-o-transform:scale(1.1);transform:scale(1.1)}
.pay_clickli{border:2px solid #3b82f6 !important;background:#dbeafe !important;background:-ms-linear-gradient(135deg,#dbeafe 0%,#bfdbfe 100%) !important;filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#dbeafe', endColorstr='#bfdbfe', GradientType=1) !important;box-shadow:0 4px 16px rgba(59,130,246,0.3) !important;filter:progid:DXImageTransform.Microsoft.Shadow(color='#3b82f6', Direction=135, Strength=4) !important}
.pay_down_ico{display:none;width:20px;height:20px;position:absolute;bottom:-7px;right:-7px;background:#1890ff;border-radius:50%;box-shadow:0 2px 6px rgba(24,144,255,.4);border:2px solid white;z-index:10}
.pay_down_ico::after{content:'✓';position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);color:white;font-size:12px;font-weight:bold;line-height:1}
.pay_clickli .pay_down_ico{display:block}
.order-header{padding:16px 20px 0;background:#fff}
.order-title{font-size:28px;font-weight:600;color:#333;margin-bottom:12px;text-align:center;display:flex;align-items:center;justify-content:center;gap:8px}
.order-amount{font-size:36px;font-weight:700;color:#ff6600;text-align:center;font-family:'SF Pro Display',-apple-system,BlinkMacSystemFont,sans-serif;margin:8px 0 16px}

.timeout-section{text-align:center;margin-bottom:20px}
.timeout-details{background:#fff;border-radius:12px;box-shadow:0 4px 20px rgba(0,0,0,.08),0 1px 3px rgba(0,0,0,.05);border:1px solid #e5e7eb;overflow:hidden;margin-top:12px;position:relative}
.timeout-details::before{content:'';position:absolute;top:0;left:0;right:0;height:2px;background:linear-gradient(90deg,#dc2626 0%,#ef4444 50%,#dc2626 100%)}
.timeout-main-message{padding:40px 24px 36px;text-align:center;background:linear-gradient(135deg,#fef2f2 0%,#fff 100%);position:relative}
.timeout-main-message::after{content:'';position:absolute;bottom:0;left:24px;right:24px;height:1px;background:linear-gradient(90deg,transparent 0%,#fecaca 50%,transparent 100%)}
.timeout-icon{font-size:56px;margin-bottom:20px;display:block;color:#dc2626;animation:gentle-bounce 2s ease-in-out infinite}
.timeout-title{font-size:20px;font-weight:700;color:#dc2626;margin-bottom:16px;line-height:1.3;letter-spacing:-.02em}
.timeout-subtitle{font-size:15px;color:#6b7280;line-height:1.6;max-width:320px;margin:0 auto;font-weight:400}
@keyframes gentle-bounce{0%,100%{transform:translateY(0)}50%{transform:translateY(-4px)}}
.timeout-order-info{padding:0;background:#fff;margin:20px 24px 0;border-radius:8px;box-shadow:0 1px 3px rgba(0,0,0,.05);border:1px solid #f1f5f9}
.timeout-info-header{padding:18px 20px 14px;background:linear-gradient(135deg,#f8fafc 0%,#f1f5f9 100%);border-bottom:1px solid #e2e8f0;border-radius:8px 8px 0 0}
.timeout-info-title{font-size:15px;font-weight:600;color:#374151}
.timeout-info-list{padding:20px;background:#fff}
.timeout-info-item{display:flex;justify-content:space-between;align-items:center;padding:14px 0;border-bottom:1px solid #f8fafc;transition:all .2s ease}
.timeout-info-item:hover{background:#f8fafc;margin:0 -12px;padding:14px 12px;border-radius:6px;border-bottom:1px solid transparent}
.timeout-info-item:last-child{border-bottom:none;padding-bottom:0}
.timeout-info-item:last-child:hover{padding-bottom:0}
.timeout-info-label{font-size:14px;color:#6b7280;font-weight:500;text-align:left;min-width:90px}
.timeout-info-value{font-size:14px;color:#1f2937;font-weight:600;text-align:right;flex:1;margin-left:16px}

.timeout-contact{padding:32px 24px 36px;text-align:center;background:linear-gradient(135deg,#f8fafc 0%,#f1f5f9 100%);margin-top:24px}
.timeout-contact-content{display:flex;align-items:center;justify-content:center;gap:10px;margin-bottom:20px}
.timeout-contact-icon{font-size:18px;opacity:.8}
.timeout-contact-text{font-size:15px;color:#6b7280;font-weight:500}
.timeout-contact-button{display:inline-flex;align-items:center;gap:8px;padding:14px 28px;background:linear-gradient(135deg,#3b82f6 0%,#2563eb 100%);color:white;text-decoration:none;border-radius:10px;font-size:15px;font-weight:600;transition:all .3s cubic-bezier(.4,0,.2,1);box-shadow:0 4px 14px rgba(59,130,246,.25);position:relative;overflow:hidden}
.timeout-contact-button::before{content:'';position:absolute;inset:0 -100% 0 -100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.15),transparent);transition:left .5s ease}
.timeout-contact-button:hover::before{left:100%}
.timeout-contact-button:hover{background:linear-gradient(135deg,#2563eb 0%,#1d4ed8 100%);transform:translateY(-2px);box-shadow:0 8px 20px rgba(59,130,246,.35);color:white}
.timeout-contact-button-icon{font-size:16px}
.qrcode-section{background:#fff;border-radius:0 0 12px 12px;padding:20px}
.qrcode-container{max-width:240px;margin:0 auto;text-align:center}
.qrcode-wrapper{position:relative;display:inline-block;padding:20px;background:white;border-radius:16px;box-shadow:0 8px 24px rgba(0,0,0,0.12), 0 2px 6px rgba(0,0,0,0.08);filter:progid:DXImageTransform.Microsoft.Shadow(color='#cccccc', Direction=135, Strength=5);margin-bottom:20px;border:1px solid #e5e7eb}
.qrcode-wrapper:hover{box-shadow:0 12px 32px rgba(0,0,0,0.15), 0 4px 8px rgba(0,0,0,0.1);filter:progid:DXImageTransform.Microsoft.Shadow(color='#999999', Direction=135, Strength=6)}
.qr-image{width:220px;height:220px;background:#fff;border-radius:8px;position:relative;overflow:hidden;margin:0 auto;text-align:center;line-height:220px}
.qr-image canvas,.qr-image img{width:100%;height:100%;border-radius:4px;vertical-align:middle;line-height:normal}
.qrcode-tip{text-align:center;margin-bottom:20px}
.scan-instruction{font-size:16px;font-weight:700;color:#1f2937;margin-bottom:8px}
.scan-instruction b{color:#3b82f6;font-weight:bold}
.scan-description{font-size:13px;color:#6b7280;margin:4px 0 0 0;font-weight:500}
.countdown-section{padding:16px 20px;border-radius:12px;margin:0 20px 16px;border:1px solid #e5e7eb}
.countdown-section.alipay{background:#cce7ff;background:-ms-linear-gradient(135deg,#cce7ff 0%,#91d5ff 100%);filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#cce7ff', endColorstr='#91d5ff', GradientType=1);box-shadow:0 4px 12px rgba(24,144,255,0.2);border:1px solid #1890ff}
.countdown-section.wechat{background:#d9f7be;background:-ms-linear-gradient(135deg,#d9f7be 0%,#b7eb8f 100%);filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#d9f7be', endColorstr='#b7eb8f', GradientType=1);box-shadow:0 4px 12px rgba(82,196,26,0.2);border:1px solid #52c41a}
.countdown-container{text-align:center;zoom:1}
.countdown-title{font-size:14px;font-weight:600;margin:0 0 8px 0;display:inline-block}
.countdown-section.alipay .countdown-title{color:#0958d9}
.countdown-section.wechat .countdown-title{color:#389e0d}
.time-item{display:inline-block;margin:0 4px}
.time-item strong{background:rgba(255,255,255,0.95);background:#ffffff;line-height:1;font-size:16px;font-weight:700;padding:8px 12px;border-radius:8px;display:inline-block;min-width:44px;text-align:center;font-family:'Microsoft YaHei',Arial,sans-serif;border:1px solid #e5e7eb}
.countdown-section.alipay .time-item strong{color:#0958d9;border:1px solid #1890ff;box-shadow:0 2px 6px rgba(9,88,217,0.2)}
.countdown-section.wechat .time-item strong{color:#389e0d;border:1px solid #52c41a;box-shadow:0 2px 6px rgba(56,158,13,0.2)}
.payment-tips{padding:12px 20px;background:#f8f9fa;border-radius:6px;margin:0 20px 16px;border:1px solid #e9ecef;border-bottom-left-radius:12px;border-bottom-right-radius:12px}
.tip-item{margin-bottom:0;padding:0;background:transparent}
.tip-text{font-size:12px;line-height:1.4;color:#6b7280;font-weight:400;display:block;width:100%}
.tip-icon{display:inline-flex;align-items:center;justify-content:center;width:16px;height:16px;border-radius:50%;font-size:10px;font-weight:700;margin-right:6px;vertical-align:middle}
.tip-icon.info{background:linear-gradient(135deg,#6b7280,#4b5563);color:white;box-shadow:0 2px 4px rgba(107,114,128,.25);border:1px solid rgba(255,255,255,.2)}
.tip-icon.warning{background:linear-gradient(135deg,#f59e0b,#d97706);color:white;box-shadow:0 1px 3px rgba(245,158,11,.3)}
.contact-service{color:#6b7280;font-weight:500;text-decoration:none;padding:4px 8px;border:1px solid #d1d5db;border-radius:4px;font-size:12px;transition:all .2s ease;background:#ffffff;margin-left:2px}
.contact-service:hover{color:#374151;background:#f9fafb;border-color:#9ca3af;text-decoration:none}
.order-details{padding:0 20px 8px;margin-top:8px}
.countdown-section.warning{background:linear-gradient(135deg,#fff7e6 0%,#ffecc7 100%)!important;box-shadow:0 4px 12px rgba(245,158,11,.2)!important;border:2px solid #fa8c16!important}
.countdown-section.warning .countdown-title{color:#d46b08;opacity:1;font-weight:700}
.countdown-section.warning .time-item strong{border:2px solid #fa8c16;background:#fff;color:#d46b08;box-shadow:0 2px 8px rgba(250,140,22,.3);font-weight:800}
.countdown-section.danger{background:linear-gradient(135deg,#fff1f0 0%,#ffccc7 100%)!important;box-shadow:0 4px 12px rgba(239,68,68,.2)!important;border:2px solid #ff4d4f!important}
.countdown-section.danger .countdown-title{color:#cf1322;opacity:1;font-weight:700}
.countdown-section.danger .time-item strong{border:2px solid #ff4d4f;background:#fff;color:#cf1322;box-shadow:0 2px 8px rgba(255,77,79,.3);font-weight:800}
.warning-blink{animation:warningBlink 1s infinite}
.danger-pulse{animation:dangerPulse .8s infinite}
@keyframes warningBlink{0%,50%{background:rgba(255,255,255,.3);transform:scale(1)}25%,75%{background:rgba(255,255,255,.5);transform:scale(1.05)}}
@keyframes dangerPulse{0%,100%{background:rgba(255,255,255,.2);transform:scale(1);box-shadow:0 0 0 0 rgba(239,68,68,.7)}50%{background:rgba(255,255,255,.4);transform:scale(1.02);box-shadow:0 0 0 8px rgba(239,68,68,0)}}
.message-layer{pointer-events:none}
.message-layer .custom-msg-content{pointer-events:auto}
.qrcode-loading,.qrcode-error{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:12px;padding:40px 20px}
.loading-spinner{width:32px;height:32px;border:3px solid #e5e7eb;border-top:3px solid #3b82f6;border-radius:50%;animation:spin 1s linear infinite}
.loading-text,.error-text{font-size:14px;color:#6c757d;margin-top:8px;text-align:center}
.error-icon{font-size:32px;color:#e53e3e}
.retry-btn{margin-top:12px;padding:10px 20px;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:white;border:none;border-radius:8px;cursor:pointer;font-size:14px;font-weight:500;transition:all .3s ease;box-shadow:0 4px 12px rgba(102,126,234,.3)}
.retry-btn:hover{transform:translateY(-2px);box-shadow:0 8px 24px rgba(102,126,234,.4)}
@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}
.ie9-fix .pay_ul2 li{float:left;margin-left:20px}
.ie9-fix .pay_ul2:after{content:"";display:table;clear:both}
.ie9-fix .countdown-container{text-align:center}
.ie9-fix .time-item{display:inline;margin:0 2px}
.ie9-fix .qr-image{text-align:center;display:table-cell;vertical-align:middle}
.ie9-fix .qr-image canvas,.ie9-fix .qr-image img{display:inline-block;vertical-align:middle}
</style>
<!--[if IE 9]>
<style>
/* IE9 专用样式 */
.pay_ul2 li {
    float: left !important;
    margin-left: 20px !important;
    display: block !important;
}
.countdown-container {
    text-align: center !important;
}
.time-item {
    display: inline !important;
    margin: 0 2px !important;
}
</style>
<![endif]-->
</head>
<body>
<div class="body" id="body">
<div class="mod-title" style="display:none">
<div class="mod-title-border"></div>
<ul class="pay_ul2">
<li id="alipayLi" class="pay_clickli" onclick="changePay(0)">
<img class="pay_fs" src="/static/image/pay/alipay.png" title="支付宝" alt="支付宝">
<div class="pay_down_ico"></div>
</li>
<li id="wxpayLi" onclick="changePay(1)">
<img class="pay_fs" src="/static/image/pay/weixin.png" title="微信支付" alt="微信支付">
<div class="pay_down_ico"></div>
</li>
</ul>
</div>
<div class="mod-ct" id="loadingDiv"><img id="loading" src="/static/image/pay/loading.gif" style="width:150px;height:150px"></div>
<div class="mod-ct" id="orderDiv" style="display:none">
<div class="order-header">
<div class="order-title">
<img id="productIcon" class="product-icon" style="display:none" alt="产品图标">
<span id="strRemark"></span>
</div>
<div class="order-amount" id="money"></div>
<div class="timeout-section" id="timeOut" style="display:none;">
<div class="timeout-details" id="timeOutTip" style="display:none">
<div class="timeout-main-message">
<div class="timeout-icon">⏰</div>
<div class="timeout-title">支付超时，订单已关闭</div>
<div class="timeout-subtitle">支付超时，订单已自动关闭<br>如需继续购买，请重新下单</div>
</div>
<div class="timeout-order-info">
<div class="timeout-info-header"><span class="timeout-info-title">订单详情</span></div>
<div class="timeout-info-list">
<div class="timeout-info-item"><span class="timeout-info-label">商品名称</span><span class="timeout-info-value" id="timeoutProductName">-</span></div>
<div class="timeout-info-item"><span class="timeout-info-label">订单编号</span><span class="timeout-info-value" id="timeoutOrderId">-</span></div>
<div class="timeout-info-item"><span class="timeout-info-label">创建时间</span><span class="timeout-info-value" id="timeoutCreateTime">-</span></div>
</div>
</div>
<div class="timeout-contact">
<div class="timeout-contact-content"><span class="timeout-contact-icon">💬</span><span class="timeout-contact-text">需要帮助？</span></div>
<a id="keFuQQ1" href="javascript:void(0)" target="_blank" class="timeout-contact-button"><span class="timeout-contact-button-icon">🎧</span><span>联系客服</span></a>
</div>
</div>
</div>
</div>
<div id="orderbody">
<div class="qrcode-section">
<div class="qrcode-container">
<div class="qrcode-wrapper"><div class="qr-image" id="qrcode"></div></div>
<div class="qrcode-tip">
<p class="scan-instruction">使用 <b id="payTypeMsg">支付宝</b> 扫一扫</p>
<p class="scan-description">扫描二维码完成支付</p>
</div>
</div>
</div>
<div class="countdown-section">
<div class="countdown-container">
<div class="countdown-title">支付剩余时间</div>
<div class="time-item">
<strong id="hour_show" style="display:none;">0时</strong>
<strong id="minute_show">0分</strong>
<strong id="second_show">0秒</strong>
</div>
</div>
</div>
<div class="payment-tips" id="msg">
<div class="tip-item" id="lblYouHui" style="display:none;"><span class="tip-text"><span class="tip-icon warning">⚠</span>请务必与订单金额一致，以免订单失败！</span></div>
<div class="tip-item"><span class="tip-text"><span class="tip-icon info">ℹ</span>支付完成后，请耐心等待页面跳转，有问题请<a id="keFuQQ" href="javascript:void(0)" target="_blank" class="contact-service">联系客服</a></span></div>
</div>
<div class="order-details">
<div class="detail" id="orderDetail">
<div class="detail-ct" id="desc" style="display:none">
<div class="detail-item"><span class="detail-label">订单金额</span><span class="detail-value">￥<b id="strPrice"></b></span></div>
<div class="detail-item"><span class="detail-label">订单编号</span><span class="detail-value"><b id="strOrderId"></b></span></div>
<div class="detail-item"><span class="detail-label">商户订单号</span><span class="detail-value"><b id="strPayId"></b></span></div>
<div class="detail-item"><span class="detail-label">创建时间</span><span class="detail-value"><b id="strDate"></b></span></div>
</div>
<a href="javascript:void(0)" class="arrow" onclick="showDetail()"><span class="arrow-text"><b>查看订单详情</b></span><i class="ico-arrow"></i></a>
</div>
</div>
</div>
</div>
</div>
    <script>
        var qrcode=function(){var t=function(t,r){var e=t,n=g[r],o=null,i=0,a=null,u=[],f={},c=function(t,r){o=function(t){for(var r=new Array(t),e=0;e<t;e+=1){r[e]=new Array(t);for(var n=0;n<t;n+=1)r[e][n]=null}return r}(i=4*e+17),l(0,0),l(i-7,0),l(0,i-7),s(),h(),d(t,r),e>=7&&v(t),null==a&&(a=p(e,n,u)),w(a,r)},l=function(t,r){for(var e=-1;e<=7;e+=1)if(!(t+e<=-1||i<=t+e))for(var n=-1;n<=7;n+=1)r+n<=-1||i<=r+n||(o[t+e][r+n]=0<=e&&e<=6&&(0==n||6==n)||0<=n&&n<=6&&(0==e||6==e)||2<=e&&e<=4&&2<=n&&n<=4)},h=function(){for(var t=8;t<i-8;t+=1)null==o[t][6]&&(o[t][6]=t%2==0);for(var r=8;r<i-8;r+=1)null==o[6][r]&&(o[6][r]=r%2==0)},s=function(){for(var t=B.getPatternPosition(e),r=0;r<t.length;r+=1)for(var n=0;n<t.length;n+=1){var i=t[r],a=t[n];if(null==o[i][a])for(var u=-2;u<=2;u+=1)for(var f=-2;f<=2;f+=1)o[i+u][a+f]=-2==u||2==u||-2==f||2==f||0==u&&0==f}},v=function(t){for(var r=B.getBCHTypeNumber(e),n=0;n<18;n+=1){var a=!t&&1==(r>>n&1);o[Math.floor(n/3)][n%3+i-8-3]=a}for(n=0;n<18;n+=1){a=!t&&1==(r>>n&1);o[n%3+i-8-3][Math.floor(n/3)]=a}},d=function(t,r){for(var e=n<<3|r,a=B.getBCHTypeInfo(e),u=0;u<15;u+=1){var f=!t&&1==(a>>u&1);u<6?o[u][8]=f:u<8?o[u+1][8]=f:o[i-15+u][8]=f}for(u=0;u<15;u+=1){f=!t&&1==(a>>u&1);u<8?o[8][i-u-1]=f:u<9?o[8][15-u-1+1]=f:o[8][15-u-1]=f}o[i-8][8]=!t},w=function(t,r){for(var e=-1,n=i-1,a=7,u=0,f=B.getMaskFunction(r),c=i-1;c>0;c-=2)for(6==c&&(c-=1);;){for(var g=0;g<2;g+=1)if(null==o[n][c-g]){var l=!1;u<t.length&&(l=1==(t[u]>>>a&1)),f(n,c-g)&&(l=!l),o[n][c-g]=l,-1==(a-=1)&&(u+=1,a=7)}if((n+=e)<0||i<=n){n-=e,e=-e;break}}},p=function(t,r,e){for(var n=A.getRSBlocks(t,r),o=b(),i=0;i<e.length;i+=1){var a=e[i];o.put(a.getMode(),4),o.put(a.getLength(),B.getLengthInBits(a.getMode(),t)),a.write(o)}var u=0;for(i=0;i<n.length;i+=1)u+=n[i].dataCount;if(o.getLengthInBits()>8*u)throw"code length overflow. ("+o.getLengthInBits()+">"+8*u+")";for(o.getLengthInBits()+4<=8*u&&o.put(0,4);o.getLengthInBits()%8!=0;)o.putBit(!1);for(;!(o.getLengthInBits()>=8*u||(o.put(236,8),o.getLengthInBits()>=8*u));)o.put(17,8);return function(t,r){for(var e=0,n=0,o=0,i=new Array(r.length),a=new Array(r.length),u=0;u<r.length;u+=1){var f=r[u].dataCount,c=r[u].totalCount-f;n=Math.max(n,f),o=Math.max(o,c),i[u]=new Array(f);for(var g=0;g<i[u].length;g+=1)i[u][g]=255&t.getBuffer()[g+e];e+=f;var l=B.getErrorCorrectPolynomial(c),h=k(i[u],l.getLength()-1).mod(l);for(a[u]=new Array(l.getLength()-1),g=0;g<a[u].length;g+=1){var s=g+h.getLength()-a[u].length;a[u][g]=s>=0?h.getAt(s):0}}var v=0;for(g=0;g<r.length;g+=1)v+=r[g].totalCount;var d=new Array(v),w=0;for(g=0;g<n;g+=1)for(u=0;u<r.length;u+=1)g<i[u].length&&(d[w]=i[u][g],w+=1);for(g=0;g<o;g+=1)for(u=0;u<r.length;u+=1)g<a[u].length&&(d[w]=a[u][g],w+=1);return d}(o,n)};f.addData=function(t,r){var e=null;switch(r=r||"Byte"){case"Numeric":e=M(t);break;case"Alphanumeric":e=x(t);break;case"Byte":e=m(t);break;case"Kanji":e=L(t);break;default:throw"mode:"+r}u.push(e),a=null},f.isDark=function(t,r){if(t<0||i<=t||r<0||i<=r)throw t+","+r;return o[t][r]},f.getModuleCount=function(){return i},f.make=function(){if(e<1){for(var t=1;t<40;t++){for(var r=A.getRSBlocks(t,n),o=b(),i=0;i<u.length;i++){var a=u[i];o.put(a.getMode(),4),o.put(a.getLength(),B.getLengthInBits(a.getMode(),t)),a.write(o)}var g=0;for(i=0;i<r.length;i++)g+=r[i].dataCount;if(o.getLengthInBits()<=8*g)break}e=t}c(!1,function(){for(var t=0,r=0,e=0;e<8;e+=1){c(!0,e);var n=B.getLostPoint(f);(0==e||t>n)&&(t=n,r=e)}return r}())},f.createTableTag=function(t,r){t=t||2;var e="";e+='<table style="',e+=" border-width: 0px; border-style: none;",e+=" border-collapse: collapse;",e+=" padding: 0px; margin: "+(r=void 0===r?4*t:r)+"px;",e+='">',e+="<tbody>";for(var n=0;n<f.getModuleCount();n+=1){e+="<tr>";for(var o=0;o<f.getModuleCount();o+=1)e+='<td style="',e+=" border-width: 0px; border-style: none;",e+=" border-collapse: collapse;",e+=" padding: 0px; margin: 0px;",e+=" width: "+t+"px;",e+=" height: "+t+"px;",e+=" background-color: ",e+=f.isDark(n,o)?"#000000":"#ffffff",e+=";",e+='"/>';e+="</tr>"}return e+="</tbody>",e+="</table>"},f.createSvgTag=function(t,r,e,n){var o={};"object"==typeof arguments[0]&&(t=(o=arguments[0]).cellSize,r=o.margin,e=o.alt,n=o.title),t=t||2,r=void 0===r?4*t:r,(e="string"==typeof e?{text:e}:e||{}).text=e.text||null,e.id=e.text?e.id||"qrcode-description":null,(n="string"==typeof n?{text:n}:n||{}).text=n.text||null,n.id=n.text?n.id||"qrcode-title":null;var i,a,u,c,g=f.getModuleCount()*t+2*r,l="";for(c="l"+t+",0 0,"+t+" -"+t+",0 0,-"+t+"z ",l+='<svg version="1.1" xmlns="http://www.w3.org/2000/svg"',l+=o.scalable?"":' width="'+g+'px" height="'+g+'px"',l+=' viewBox="0 0 '+g+" "+g+'" ',l+=' preserveAspectRatio="xMinYMin meet"',l+=n.text||e.text?' role="img" aria-labelledby="'+y([n.id,e.id].join(" ").trim())+'"':"",l+=">",l+=n.text?'<title id="'+y(n.id)+'">'+y(n.text)+"</title>":"",l+=e.text?'<description id="'+y(e.id)+'">'+y(e.text)+"</description>":"",l+='<rect width="100%" height="100%" fill="white" cx="0" cy="0"/>',l+='<path d="',a=0;a<f.getModuleCount();a+=1)for(u=a*t+r,i=0;i<f.getModuleCount();i+=1)f.isDark(a,i)&&(l+="M"+(i*t+r)+","+u+c);return l+='" stroke="transparent" fill="black"/>',l+="</svg>"},f.createDataURL=function(t,r){t=t||2,r=void 0===r?4*t:r;var e=f.getModuleCount()*t+2*r,n=r,o=e-r;return I(e,e,(function(r,e){if(n<=r&&r<o&&n<=e&&e<o){var i=Math.floor((r-n)/t),a=Math.floor((e-n)/t);return f.isDark(a,i)?0:1}return 1}))},f.createImgTag=function(t,r,e){t=t||2,r=void 0===r?4*t:r;var n=f.getModuleCount()*t+2*r,o="";return o+="<img",o+=' src="',o+=f.createDataURL(t,r),o+='"',o+=' width="',o+=n,o+='"',o+=' height="',o+=n,o+='"',e&&(o+=' alt="',o+=y(e),o+='"'),o+="/>"};var y=function(t){for(var r="",e=0;e<t.length;e+=1){var n=t.charAt(e);switch(n){case"<":r+="&lt;";break;case">":r+="&gt;";break;case"&":r+="&amp;";break;case'"':r+="&quot;";break;default:r+=n}}return r};return f.createASCII=function(t,r){if((t=t||1)<2)return function(t){t=void 0===t?2:t;var r,e,n,o,i,a=1*f.getModuleCount()+2*t,u=t,c=a-t,g={"██":"█","█ ":"▀"," █":"▄","  ":" "},l={"██":"▀","█ ":"▀"," █":" ","  ":" "},h="";for(r=0;r<a;r+=2){for(n=Math.floor((r-u)/1),o=Math.floor((r+1-u)/1),e=0;e<a;e+=1)i="█",u<=e&&e<c&&u<=r&&r<c&&f.isDark(n,Math.floor((e-u)/1))&&(i=" "),u<=e&&e<c&&u<=r+1&&r+1<c&&f.isDark(o,Math.floor((e-u)/1))?i+=" ":i+="█",h+=t<1&&r+1>=c?l[i]:g[i];h+="\n"}return a%2&&t>0?h.substring(0,h.length-a-1)+Array(a+1).join("▀"):h.substring(0,h.length-1)}(r);t-=1,r=void 0===r?2*t:r;var e,n,o,i,a=f.getModuleCount()*t+2*r,u=r,c=a-r,g=Array(t+1).join("██"),l=Array(t+1).join("  "),h="",s="";for(e=0;e<a;e+=1){for(o=Math.floor((e-u)/t),s="",n=0;n<a;n+=1)i=1,u<=n&&n<c&&u<=e&&e<c&&f.isDark(o,Math.floor((n-u)/t))&&(i=0),s+=i?g:l;for(o=0;o<t;o+=1)h+=s+"\n"}return h.substring(0,h.length-1)},f.renderTo2dContext=function(t,r){r=r||2;for(var e=f.getModuleCount(),n=0;n<e;n++)for(var o=0;o<e;o++)t.fillStyle=f.isDark(n,o)?"black":"white",t.fillRect(n*r,o*r,r,r)},f};t.stringToBytes=(t.stringToBytesFuncs={default:function(t){for(var r=[],e=0;e<t.length;e+=1){var n=t.charCodeAt(e);r.push(255&n)}return r}}).default,t.createStringToBytes=function(t,r){var e=function(){for(var e=S(t),n=function(){var t=e.read();if(-1==t)throw"eof";return t},o=0,i={};;){var a=e.read();if(-1==a)break;var u=n(),f=n()<<8|n();i[String.fromCharCode(a<<8|u)]=f,o+=1}if(o!=r)throw o+" != "+r;return i}(),n="?".charCodeAt(0);return function(t){for(var r=[],o=0;o<t.length;o+=1){var i=t.charCodeAt(o);if(i<128)r.push(i);else{var a=e[t.charAt(o)];"number"==typeof a?(255&a)==a?r.push(a):(r.push(a>>>8),r.push(255&a)):r.push(n)}}return r}};var r,e,n,o,i,a=1,u=2,f=4,c=8,g={L:1,M:0,Q:3,H:2},l=0,h=1,s=2,v=3,d=4,w=5,p=6,y=7,B=(r=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],e=1335,n=7973,i=function(t){for(var r=0;0!=t;)r+=1,t>>>=1;return r},(o={}).getBCHTypeInfo=function(t){for(var r=t<<10;i(r)-i(e)>=0;)r^=e<<i(r)-i(e);return 21522^(t<<10|r)},o.getBCHTypeNumber=function(t){for(var r=t<<12;i(r)-i(n)>=0;)r^=n<<i(r)-i(n);return t<<12|r},o.getPatternPosition=function(t){return r[t-1]},o.getMaskFunction=function(t){switch(t){case l:return function(t,r){return(t+r)%2==0};case h:return function(t,r){return t%2==0};case s:return function(t,r){return r%3==0};case v:return function(t,r){return(t+r)%3==0};case d:return function(t,r){return(Math.floor(t/2)+Math.floor(r/3))%2==0};case w:return function(t,r){return t*r%2+t*r%3==0};case p:return function(t,r){return(t*r%2+t*r%3)%2==0};case y:return function(t,r){return(t*r%3+(t+r)%2)%2==0};default:throw"bad maskPattern:"+t}},o.getErrorCorrectPolynomial=function(t){for(var r=k([1],0),e=0;e<t;e+=1)r=r.multiply(k([1,C.gexp(e)],0));return r},o.getLengthInBits=function(t,r){if(1<=r&&r<10)switch(t){case a:return 10;case u:return 9;case f:case c:return 8;default:throw"mode:"+t}else if(r<27)switch(t){case a:return 12;case u:return 11;case f:return 16;case c:return 10;default:throw"mode:"+t}else{if(!(r<41))throw"type:"+r;switch(t){case a:return 14;case u:return 13;case f:return 16;case c:return 12;default:throw"mode:"+t}}},o.getLostPoint=function(t){for(var r=t.getModuleCount(),e=0,n=0;n<r;n+=1)for(var o=0;o<r;o+=1){for(var i=0,a=t.isDark(n,o),u=-1;u<=1;u+=1)if(!(n+u<0||r<=n+u))for(var f=-1;f<=1;f+=1)o+f<0||r<=o+f||0==u&&0==f||a==t.isDark(n+u,o+f)&&(i+=1);i>5&&(e+=3+i-5)}for(n=0;n<r-1;n+=1)for(o=0;o<r-1;o+=1){var c=0;t.isDark(n,o)&&(c+=1),t.isDark(n+1,o)&&(c+=1),t.isDark(n,o+1)&&(c+=1),t.isDark(n+1,o+1)&&(c+=1),0!=c&&4!=c||(e+=3)}for(n=0;n<r;n+=1)for(o=0;o<r-6;o+=1)t.isDark(n,o)&&!t.isDark(n,o+1)&&t.isDark(n,o+2)&&t.isDark(n,o+3)&&t.isDark(n,o+4)&&!t.isDark(n,o+5)&&t.isDark(n,o+6)&&(e+=40);for(o=0;o<r;o+=1)for(n=0;n<r-6;n+=1)t.isDark(n,o)&&!t.isDark(n+1,o)&&t.isDark(n+2,o)&&t.isDark(n+3,o)&&t.isDark(n+4,o)&&!t.isDark(n+5,o)&&t.isDark(n+6,o)&&(e+=40);var g=0;for(o=0;o<r;o+=1)for(n=0;n<r;n+=1)t.isDark(n,o)&&(g+=1);return e+=Math.abs(100*g/r/r-50)/5*10},o),C=function(){for(var t=new Array(256),r=new Array(256),e=0;e<8;e+=1)t[e]=1<<e;for(e=8;e<256;e+=1)t[e]=t[e-4]^t[e-5]^t[e-6]^t[e-8];for(e=0;e<255;e+=1)r[t[e]]=e;var n={glog:function(t){if(t<1)throw"glog("+t+")";return r[t]},gexp:function(r){for(;r<0;)r+=255;for(;r>=256;)r-=255;return t[r]}};return n}();function k(t,r){if(void 0===t.length)throw t.length+"/"+r;var e=function(){for(var e=0;e<t.length&&0==t[e];)e+=1;for(var n=new Array(t.length-e+r),o=0;o<t.length-e;o+=1)n[o]=t[o+e];return n}(),n={getAt:function(t){return e[t]},getLength:function(){return e.length},multiply:function(t){for(var r=new Array(n.getLength()+t.getLength()-1),e=0;e<n.getLength();e+=1)for(var o=0;o<t.getLength();o+=1)r[e+o]^=C.gexp(C.glog(n.getAt(e))+C.glog(t.getAt(o)));return k(r,0)},mod:function(t){if(n.getLength()-t.getLength()<0)return n;for(var r=C.glog(n.getAt(0))-C.glog(t.getAt(0)),e=new Array(n.getLength()),o=0;o<n.getLength();o+=1)e[o]=n.getAt(o);for(o=0;o<t.getLength();o+=1)e[o]^=C.gexp(C.glog(t.getAt(o))+r);return k(e,0).mod(t)}};return n}var A=function(){var t=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12,7,37,13],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],r=function(t,r){var e={};return e.totalCount=t,e.dataCount=r,e},e={};return e.getRSBlocks=function(e,n){var o=function(r,e){switch(e){case g.L:return t[4*(r-1)+0];case g.M:return t[4*(r-1)+1];case g.Q:return t[4*(r-1)+2];case g.H:return t[4*(r-1)+3];default:return}}(e,n);if(void 0===o)throw"bad rs block @ typeNumber:"+e+"/errorCorrectionLevel:"+n;for(var i=o.length/3,a=[],u=0;u<i;u+=1)for(var f=o[3*u+0],c=o[3*u+1],l=o[3*u+2],h=0;h<f;h+=1)a.push(r(c,l));return a},e}(),b=function(){var t=[],r=0,e={getBuffer:function(){return t},getAt:function(r){var e=Math.floor(r/8);return 1==(t[e]>>>7-r%8&1)},put:function(t,r){for(var n=0;n<r;n+=1)e.putBit(1==(t>>>r-n-1&1))},getLengthInBits:function(){return r},putBit:function(e){var n=Math.floor(r/8);t.length<=n&&t.push(0),e&&(t[n]|=128>>>r%8),r+=1}};return e},M=function(t){var r=a,e=t,n={getMode:function(){return r},getLength:function(t){return e.length},write:function(t){for(var r=e,n=0;n+2<r.length;)t.put(o(r.substring(n,n+3)),10),n+=3;n<r.length&&(r.length-n==1?t.put(o(r.substring(n,n+1)),4):r.length-n==2&&t.put(o(r.substring(n,n+2)),7))}},o=function(t){for(var r=0,e=0;e<t.length;e+=1)r=10*r+i(t.charAt(e));return r},i=function(t){if("0"<=t&&t<="9")return t.charCodeAt(0)-"0".charCodeAt(0);throw"illegal char :"+t};return n},x=function(t){var r=u,e=t,n={getMode:function(){return r},getLength:function(t){return e.length},write:function(t){for(var r=e,n=0;n+1<r.length;)t.put(45*o(r.charAt(n))+o(r.charAt(n+1)),11),n+=2;n<r.length&&t.put(o(r.charAt(n)),6)}},o=function(t){if("0"<=t&&t<="9")return t.charCodeAt(0)-"0".charCodeAt(0);if("A"<=t&&t<="Z")return t.charCodeAt(0)-"A".charCodeAt(0)+10;switch(t){case" ":return 36;case"$":return 37;case"%":return 38;case"*":return 39;case"+":return 40;case"-":return 41;case".":return 42;case"/":return 43;case":":return 44;default:throw"illegal char :"+t}};return n},m=function(r){var e=f,n=t.stringToBytes(r),o={getMode:function(){return e},getLength:function(t){return n.length},write:function(t){for(var r=0;r<n.length;r+=1)t.put(n[r],8)}};return o},L=function(r){var e=c,n=t.stringToBytesFuncs.SJIS;if(!n)throw"sjis not supported.";!function(t,r){var e=n("友");if(2!=e.length||38726!=(e[0]<<8|e[1]))throw"sjis not supported."}();var o=n(r),i={getMode:function(){return e},getLength:function(t){return~~(o.length/2)},write:function(t){for(var r=o,e=0;e+1<r.length;){var n=(255&r[e])<<8|255&r[e+1];if(33088<=n&&n<=40956)n-=33088;else{if(!(57408<=n&&n<=60351))throw"illegal char at "+(e+1)+"/"+n;n-=49472}n=192*(n>>>8&255)+(255&n),t.put(n,13),e+=2}if(e<r.length)throw"illegal char at "+(e+1)}};return i},D=function(){var t=[],r={writeByte:function(r){t.push(255&r)},writeShort:function(t){r.writeByte(t),r.writeByte(t>>>8)},writeBytes:function(t,e,n){e=e||0,n=n||t.length;for(var o=0;o<n;o+=1)r.writeByte(t[o+e])},writeString:function(t){for(var e=0;e<t.length;e+=1)r.writeByte(t.charCodeAt(e))},toByteArray:function(){return t},toString:function(){var r="";r+="[";for(var e=0;e<t.length;e+=1)e>0&&(r+=","),r+=t[e];return r+="]"}};return r},S=function(t){var r=t,e=0,n=0,o=0,i={read:function(){for(;o<8;){if(e>=r.length){if(0==o)return-1;throw"unexpected end of file./"+o}var t=r.charAt(e);if(e+=1,"="==t)return o=0,-1;t.match(/^\s$/)||(n=n<<6|a(t.charCodeAt(0)),o+=6)}var i=n>>>o-8&255;return o-=8,i}},a=function(t){if(65<=t&&t<=90)return t-65;if(97<=t&&t<=122)return t-97+26;if(48<=t&&t<=57)return t-48+52;if(43==t)return 62;if(47==t)return 63;throw"c:"+t};return i},I=function(t,r,e){for(var n=function(t,r){var e=t,n=r,o=new Array(t*r),i={setPixel:function(t,r,n){o[r*e+t]=n},write:function(t){t.writeString("GIF87a"),t.writeShort(e),t.writeShort(n),t.writeByte(128),t.writeByte(0),t.writeByte(0),t.writeByte(0),t.writeByte(0),t.writeByte(0),t.writeByte(255),t.writeByte(255),t.writeByte(255),t.writeString(","),t.writeShort(0),t.writeShort(0),t.writeShort(e),t.writeShort(n),t.writeByte(0);var r=a(2);t.writeByte(2);for(var o=0;r.length-o>255;)t.writeByte(255),t.writeBytes(r,o,255),o+=255;t.writeByte(r.length-o),t.writeBytes(r,o,r.length-o),t.writeByte(0),t.writeString(";")}},a=function(t){for(var r=1<<t,e=1+(1<<t),n=t+1,i=u(),a=0;a<r;a+=1)i.add(String.fromCharCode(a));i.add(String.fromCharCode(r)),i.add(String.fromCharCode(e));var f,c,g,l=D(),h=(f=l,c=0,g=0,{write:function(t,r){if(t>>>r!=0)throw"length over";for(;c+r>=8;)f.writeByte(255&(t<<c|g)),r-=8-c,t>>>=8-c,g=0,c=0;g|=t<<c,c+=r},flush:function(){c>0&&f.writeByte(g)}});h.write(r,n);var s=0,v=String.fromCharCode(o[s]);for(s+=1;s<o.length;){var d=String.fromCharCode(o[s]);s+=1,i.contains(v+d)?v+=d:(h.write(i.indexOf(v),n),i.size()<4095&&(i.size()==1<<n&&(n+=1),i.add(v+d)),v=d)}return h.write(i.indexOf(v),n),h.write(e,n),h.flush(),l.toByteArray()},u=function(){var t={},r=0,e={add:function(n){if(e.contains(n))throw"dup key:"+n;t[n]=r,r+=1},size:function(){return r},indexOf:function(r){return t[r]},contains:function(r){return void 0!==t[r]}};return e};return i}(t,r),o=0;o<r;o+=1)for(var i=0;i<t;i+=1)n.setPixel(i,o,e(i,o));var a=D();n.write(a);for(var u=function(){var t=0,r=0,e=0,n="",o={},i=function(t){n+=String.fromCharCode(a(63&t))},a=function(t){if(t<0);else{if(t<26)return 65+t;if(t<52)return t-26+97;if(t<62)return t-52+48;if(62==t)return 43;if(63==t)return 47}throw"n:"+t};return o.writeByte=function(n){for(t=t<<8|255&n,r+=8,e+=1;r>=6;)i(t>>>r-6),r-=6},o.flush=function(){if(r>0&&(i(t<<6-r),t=0,r=0),e%3!=0)for(var o=3-e%3,a=0;a<o;a+=1)n+="="},o.toString=function(){return n},o}(),f=a.toByteArray(),c=0;c<f.length;c+=1)u.writeByte(f[c]);return u.flush(),"data:image/gif;base64,"+u};return t}();qrcode.stringToBytesFuncs["UTF-8"]=function(t){return function(t){for(var r=[],e=0;e<t.length;e++){var n=t.charCodeAt(e);n<128?r.push(n):n<2048?r.push(192|n>>6,128|63&n):n<55296||n>=57344?r.push(224|n>>12,128|n>>6&63,128|63&n):(e++,n=65536+((1023&n)<<10|1023&t.charCodeAt(e)),r.push(240|n>>18,128|n>>12&63,128|n>>6&63,128|63&n))}return r}(t)};
    </script>
<script>
// 浏览器兼容性检测和处理
(function() {
  var docEl = document.documentElement;
  var isIE9 = navigator.userAgent.indexOf('MSIE 9') !== -1;
  var isIE = navigator.userAgent.indexOf('MSIE') !== -1 || navigator.userAgent.indexOf('Trident') !== -1;
  var supportsFlexbox = (function() {
    var div = document.createElement('div');
    div.style.display = 'flex';
    return div.style.display === 'flex';
  })();
  if (isIE9) {
    docEl.className += ' ie9-fix';
  }
  if (!supportsFlexbox) {
    docEl.className += ' no-flexbox';
  }
  var domReady = function() {
    if (isIE9 || !supportsFlexbox) {
      var payUl = document.querySelector('.pay_ul2');
      if (payUl) {
        payUl.style.textAlign = 'center';
        var lis = payUl.querySelectorAll('li');
        for (var i = 0; i < lis.length; i++) {
          lis[i].style.display = 'inline-block';
          lis[i].style.margin = '0 8px';
          lis[i].style.verticalAlign = 'top';
        }
      }
    }
  };
  if (document.addEventListener) {
    document.addEventListener('DOMContentLoaded', domReady);
  } else if (document.attachEvent) {
    document.attachEvent('onreadystatechange', function() {
      if (document.readyState === 'complete') {
        domReady();
      }
    });
  }
})();
if (!Object.keys) {
  Object.keys = function(obj) {
    var keys = [];
    for (var key in obj) {
      if (obj.hasOwnProperty(key)) {
        keys.push(key);
      }
    }
    return keys;
  };
}
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
var CONFIG = {
  QRCODE_SIZE: 220,
  CHECK_INTERVAL: 1800,
  TICK_INTERVAL: 1000
};
var $ = function $(s) {
  return typeof s !== 'string' ? s : /^[a-zA-Z_][a-zA-Z0-9_-]*$/.test(s) && !/[.#\[\]:>~+\s]/.test(s) ? document.getElementById(s) : document.querySelector(s);
};
var DOMUtils = {
  addClass: function addClass(el, cls) {
    if (!el || !cls) return;
    if (el.classList) {
      el.classList.add(cls);
    } else {
      var className = el.className;
      if ((' ' + className + ' ').indexOf(' ' + cls + ' ') === -1) {
        el.className = className + (className ? ' ' : '') + cls;
      }
    }
  },
  removeClass: function removeClass(el, cls) {
    if (!el || !cls) return;
    if (el.classList) {
      el.classList.remove(cls);
    } else {
      var className = el.className;
      var reg = new RegExp('(^|\\s)' + cls + '(\\s|$)', 'g');
      el.className = className.replace(reg, ' ').replace(/\s+/g, ' ').replace(/^\s|\s$/g, '');
    }
  },
  hasClass: function hasClass(el, cls) {
    if (!el || !cls) return false;
    if (el.classList) {
      return el.classList.contains(cls);
    } else {
      return (' ' + el.className + ' ').indexOf(' ' + cls + ' ') !== -1;
    }
  },
  show: function show(el, d) {
    return el && (el.style.display = d || '');
  },
  hide: function hide(el) {
    return el && (el.style.display = 'none');
  },
  html: function html(el, c) {
    return !el ? null : c !== undefined ? (el.innerHTML = c, el) : el.innerHTML;
  },
  text: function text(el, c) {
    return !el ? null : c !== undefined ? (el.textContent = c, el) : el.textContent;
  },
  css: function css(el, p, v) {
    if (!(el !== null && el !== void 0 && el.style)) return null;
    if (_typeof(p) === 'object') {
      for (var k in p) p.hasOwnProperty(k) && (el.style[k] = p[k]);
      return el;
    }
    return v !== undefined ? (el.style[p] = v, el) : (window.getComputedStyle ? getComputedStyle(el)[p] : el.currentStyle[p]);
  },
  attr: function attr(el, n, v) {
    return !el ? null : v !== undefined ? (el.setAttribute(n, v), el) : el.getAttribute(n);
  }
};
var AjaxUtils = {
  post: function post(url, data, cb, errCb) {
    var xhr = new XMLHttpRequest();
    xhr.open('POST', url, true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    xhr.onreadystatechange = function () {
      if (xhr.readyState === 4) {
        if (xhr.status === 200) {
          try {
            cb && cb(JSON.parse(xhr.responseText));
          } catch (e) {
            errCb && errCb('数据解析失败');
          }
        } else {
          errCb && errCb('网络请求失败');
        }
      }
    };
    xhr.onerror = function () {
      return errCb && errCb('网络连接失败');
    };
    xhr.send(data);
  }
};
var QRCode = {
  generate: function generate(text, opts) {
    opts = opts || {};
    var size = opts.width || opts.height || CONFIG.QRCODE_SIZE,
      canvas = document.createElement('canvas'),
      ctx = canvas.getContext('2d');
    canvas.width = canvas.height = size;
    try {
      var qr = qrcode(0, 'M');
      qr.addData(text);
      qr.make();
      var moduleCount = qr.getModuleCount(),
        cellSize = size / moduleCount;
      ctx.fillStyle = opts.background || '#ffffff';
      ctx.fillRect(0, 0, size, size);
      ctx.fillStyle = opts.foreground || '#000000';
      for (var row = 0; row < moduleCount; row++) for (var col = 0; col < moduleCount; col++) qr.isDark(row, col) && ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
    } catch (error) {
      ctx.fillStyle = '#f5f5f5';
      ctx.fillRect(0, 0, size, size);
      ctx.fillStyle = '#d9d9d9';
      ctx.font = '16px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('二维码生成失败', size / 2, size / 2);
    }
    return canvas;
  }
};
var customMsgTimer = null,
  intDiff = 0,
  currentUIState = 'loading';
function showLoadingState() {
  currentUIState = 'loading';
  DOMUtils.show($('loadingDiv'));
  DOMUtils.hide($('orderDiv'));
  DOMUtils.hide($('.mod-title'));
  DOMUtils.hide($('timeOut'));
}
function showNormalState(data) {
  currentUIState = 'normal';
  DOMUtils.hide($('loadingDiv'));
  DOMUtils.hide($('timeOut'));
  DOMUtils.show($('orderDiv'));
  DOMUtils.show($('.order-title'));
  DOMUtils.show($('.order-amount'));
  DOMUtils.show($('orderbody'));
  DOMUtils.show($('.countdown-section'));
  data && data.timeLeft > 0 && PaymentModule.orderFrom != 1 && DOMUtils.show($('.mod-title'));
  data && data.needUserPay && DOMUtils.show($('lblYouHui'));
}
function showTimeoutState(data) {
  currentUIState = 'timeout';
  var loadingDiv = $('loadingDiv'),
    orderDiv = $('orderDiv'),
    timeOut = $('timeOut');
  DOMUtils.hide(loadingDiv);
  DOMUtils.show(orderDiv);
  DOMUtils.hide($('.mod-title'));
  DOMUtils.hide($('orderbody'));
  DOMUtils.hide($('.order-title'));
  DOMUtils.hide($('.order-amount'));
  DOMUtils.show(timeOut);
  data && data.orderData && fillTimeoutOrderData(data.orderData);
  if (data && data.message && data.message.indexOf('成功') > -1) DOMUtils.hide($('timeOutTip'));else if (!data || !data.message || data.message === '') DOMUtils.show($('timeOutTip'));
  if (timeOut) {
    timeOut.style.cssText = 'opacity:0;transform:translateY(20px);transition:all .5s ease';
    setTimeout(function () {
      return timeOut.style.cssText = 'opacity:1;transform:translateY(0);transition:all .5s ease';
    }, 100);
  }
}
function showSuccessState(data) {
  currentUIState = 'success';
  showTimeoutState({
    message: '恭喜，订单支付成功!',
    orderData: data ? data.orderData : null
  });
  data && data.returnUrl && (window.location.href = data.returnUrl);
}
function fillTimeoutOrderData(orderData) {
  if (!orderData) return;
  var updates = {
    timeoutProductName: orderData.remark,
    timeoutOrderId: orderData.orderId,
    timeoutCreateTime: formatDate(orderData.date),
    timeoutAmount: "￥" + orderData.reallyPrice.toFixed(2),
    strRemark: orderData.remark,
    money: "￥" + orderData.reallyPrice.toFixed(2),
    strPrice: orderData.price,
    strPayId: orderData.payId,
    strOrderId: orderData.orderId,
    strDate: formatDate(orderData.date)
  };
  Object.keys(updates).forEach(function (id) {
    var el = $(id);
    el && DOMUtils.text(el, updates[id]);
  });
}
var MessageModule = {
  show: function show(content, timeout, type) {
    type = type || 'success';
    content = content.replace(/\n/g, '<br>');
    var msgLayer = $('customMsgLayer') || this.createMessageLayer();
    this.updateMessageContent(msgLayer, content, type);
    this.positionMessage(msgLayer);
    this.showMessage(msgLayer, timeout);
  },
  createMessageLayer: function createMessageLayer() {
    var msgLayer = document.createElement('div');
    msgLayer.id = 'customMsgLayer';
    msgLayer.className = 'message-layer';
    document.body.appendChild(msgLayer);
    return msgLayer;
  },
  updateMessageContent: function updateMessageContent(msgLayer, content, type) {
    var iconMap = {
      success: {
        color: '#52c41a',
        path: 'M768 320L448 640l-192-192'
      },
      error: {
        color: '#f5222d',
        path: 'M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm165.4 618.2l-66-.3L512 563.4l-99.3 118.4-66.1.3c-4.4 0-8-3.5-8-8 0-1.9.7-3.7 1.9-5.2l130.1-155L340.5 359a8.32 8.32 0 0 1-1.9-5.2c0-4.4 3.6-8 8-8l66.1.3L512 464.6l99.3-118.4 66-.3c4.4 0 8 3.5 8 8 0 1.9-.7 3.7-1.9 5.2L553.5 514l130 155c1.2 1.5 1.9 3.3 1.9 5.2 0 4.4-3.6 8-8 8z'
      },
      warning: {
        color: '#fa8c16',
        path: 'M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z'
      }
    };
    var icon = iconMap[type] || iconMap.success;
    msgLayer.innerHTML = '<div class="custom-msg-content"><span class="custom-msg-icon"><svg viewBox="0 0 1024 1024" width="20" height="20"><circle cx="512" cy="512" r="512" fill="' + icon.color + '"/><path d="' + icon.path + '" stroke="#fff" stroke-width="60" stroke-linecap="round" stroke-linejoin="round" fill="none"/></svg></span><span class="custom-msg-text">' + content + '</span></div>';
  },
  positionMessage: function positionMessage(msgLayer) {
    DOMUtils.css(msgLayer, {
      position: 'fixed',
      top: '120px',
      left: '50%',
      transform: 'translateX(-50%) translateY(-10px)',
      background: '#fff',
      color: '#333',
      border: 'none',
      borderRadius: '12px',
      padding: '12px 16px',
      zIndex: '19891015',
      boxShadow: '0 8px 24px rgba(0,0,0,.12), 0 2px 6px rgba(0,0,0,.08)',
      fontSize: '13px',
      minWidth: '200px',
      maxWidth: '320px',
      textAlign: 'center',
      opacity: '0',
      transition: 'all .3s cubic-bezier(.4,0,.2,1)',
      backdropFilter: 'blur(10px)'
    });
    var contentEl = msgLayer.querySelector('.custom-msg-content'),
      textEl = msgLayer.querySelector('.custom-msg-text'),
      iconEl = msgLayer.querySelector('.custom-msg-icon');
    contentEl && DOMUtils.css(contentEl, {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '8px',
      lineHeight: '1.4'
    });
    textEl && DOMUtils.css(textEl, {
      wordBreak: 'break-word',
      textAlign: 'center'
    });
    iconEl && DOMUtils.css(iconEl, {
      flexShrink: '0'
    });
  },
  showMessage: function showMessage(msgLayer, timeout) {
    customMsgTimer && (clearTimeout(customMsgTimer), customMsgTimer = null);
    msgLayer.style.display = 'block';
    setTimeout(function () {
      msgLayer.style.opacity = '1';
      msgLayer.style.transform = 'translateX(-50%) translateY(0)';
    }, 10);
    customMsgTimer = setTimeout(function () {
      return MessageModule.hide(msgLayer);
    }, timeout || 3000);
  },
  hide: function hide(msgLayer) {
    if (!msgLayer) return;
    msgLayer.style.opacity = '0';
    msgLayer.style.transform = 'translateX(-50%) translateY(-10px)';
    setTimeout(function () {
      return msgLayer.style.display = 'none';
    }, 300);
  },
  success: function success(content, timeout) {
    this.show(content, timeout, 'success');
  },
  error: function error(content, timeout) {
    this.show(content, timeout, 'error');
  },
  warning: function warning(content, timeout) {
    this.show(content, timeout, 'warning');
  }
};
function formatDate(timestamp) {
  var date = new Date(timestamp),
    pad = function pad(num) {
      return num > 9 ? num : "0" + num;
    };
  return date.getFullYear() + "-" + pad(date.getMonth() + 1) + "-" + pad(date.getDate()) + " " + pad(date.getHours()) + ":" + pad(date.getMinutes()) + ":" + pad(date.getSeconds());
}
var TimeUtils = {
  formatDuration: function formatDuration(seconds) {
    var hours = Math.floor(seconds / 3600),
      minutes = Math.floor(seconds % 3600 / 60),
      secs = Math.floor(seconds % 60),
      parts = [];
    hours > 0 && parts.push(hours + '时');
    minutes > 0 && parts.push(minutes + '分');
    (secs > 0 || parts.length === 0) && parts.push(secs + '秒');
    return parts.join(' ');
  },
  pad: function pad(num) {
    return num <= 9 ? '0' + num : num;
  }
};
var TimerModule = {
  tickTimer: null,
  checkTimer: null,
  isWarning: false,
  isDanger: false,
  warningShown: {
    fiveMinutes: false,
    threeMinutes: false,
    oneMinute: false
  },
  start: function start() {
    if (intDiff > 0) {
      this.clear();
      this.tickTimer = setInterval(function () {
        TimerModule.payTick();
        intDiff--;
      }, CONFIG.TICK_INTERVAL);
      this.checkTimer = setInterval(function () {
        return PaymentModule.check();
      }, CONFIG.CHECK_INTERVAL);
    } else PaymentModule.timeout('');
  },
  clear: function clear() {
    this.tickTimer && (clearInterval(this.tickTimer), this.tickTimer = null);
    this.checkTimer && (clearInterval(this.checkTimer), this.checkTimer = null);
    this.isWarning = this.isDanger = false;
    this.warningShown = {
      fiveMinutes: false,
      threeMinutes: false,
      oneMinute: false
    };
  },
  payTick: function payTick() {
    var totalSeconds = Math.max(0, intDiff),
      hours = Math.floor(totalSeconds / 3600),
      minutes = Math.floor(totalSeconds % 3600 / 60),
      seconds = Math.floor(totalSeconds % 60);
    var pad = TimeUtils.pad,
      hourShow = $('hour_show'),
      minuteShow = $('minute_show'),
      secondShow = $('second_show');
    if (hours > 0) {
      DOMUtils.html(hourShow, hours + '时');
      DOMUtils.show(hourShow);
    } else DOMUtils.hide(hourShow);
    DOMUtils.html(minuteShow, pad(minutes) + '分');
    DOMUtils.html(secondShow, pad(seconds) + '秒');
    this.updateWarningState(totalSeconds);
    if (totalSeconds <= 0) {
      PaymentModule.timeout('');
      this.clear();
    }
  },
  updateWarningState: function updateWarningState(totalSeconds) {
    var countdownSection = $('.countdown-section'),
      timeItems = document.querySelectorAll('.time-item strong');
    this.checkTimeWarnings(totalSeconds);
    if (totalSeconds <= 180 && totalSeconds > 0) {
      if (!this.isDanger) {
        this.isDanger = true;
        this.isWarning = false;
        countdownSection && (DOMUtils.removeClass(countdownSection, 'warning'), DOMUtils.addClass(countdownSection, 'danger'));
        for (var i = 0; i < timeItems.length; i++) {
          DOMUtils.removeClass(timeItems[i], 'warning-blink');
          DOMUtils.addClass(timeItems[i], 'danger-pulse');
        }
      }
    } else if (totalSeconds <= 300 && totalSeconds > 180) {
      if (!this.isWarning) {
        this.isWarning = true;
        this.isDanger = false;
        countdownSection && (DOMUtils.removeClass(countdownSection, 'danger'), DOMUtils.addClass(countdownSection, 'warning'));
        for (var i = 0; i < timeItems.length; i++) {
          DOMUtils.removeClass(timeItems[i], 'danger-pulse');
          DOMUtils.addClass(timeItems[i], 'warning-blink');
        }
      }
    } else if ((this.isWarning || this.isDanger) && totalSeconds > 300) {
      this.isWarning = this.isDanger = false;
      if (countdownSection) {
        DOMUtils.removeClass(countdownSection, 'warning');
        DOMUtils.removeClass(countdownSection, 'danger');
        var isAlipay = PaymentModule.nowPayType == 0 || PaymentModule.nowPayType == 2;
        DOMUtils.addClass(countdownSection, isAlipay ? 'alipay' : 'wechat');
      }
      for (var i = 0; i < timeItems.length; i++) {
        DOMUtils.removeClass(timeItems[i], 'warning-blink');
        DOMUtils.removeClass(timeItems[i], 'danger-pulse');
      }
    }
  },
  checkTimeWarnings: function checkTimeWarnings(totalSeconds) {
    if (totalSeconds <= 300 && totalSeconds > 290 && !this.warningShown.fiveMinutes) {
      this.warningShown.fiveMinutes = true;
      MessageModule.warning('支付时间还剩5分钟，请尽快完成支付！', 4000);
    }
    if (totalSeconds <= 180 && totalSeconds > 170 && !this.warningShown.threeMinutes) {
      this.warningShown.threeMinutes = true;
      MessageModule.error('支付时间还剩3分钟，请尽快完成支付！', 5000);
    }
    if (totalSeconds <= 60 && totalSeconds > 50 && !this.warningShown.oneMinute) {
      this.warningShown.oneMinute = true;
      MessageModule.error('支付时间剩余不到1分钟，订单即将超时！', 6000);
    }
  }
};
function fillTimeoutOrderInfo() {
  var orderId = getQueryString("orderId"),
    orderDate = $('strDate'),
    productName = $('strRemark'),
    timeoutProductName = $('timeoutProductName'),
    timeoutOrderId = $('timeoutOrderId'),
    timeoutCreateTime = $('timeoutCreateTime');
  timeoutProductName && productName && DOMUtils.text(timeoutProductName, DOMUtils.text(productName));
  timeoutOrderId && orderId && DOMUtils.text(timeoutOrderId, orderId);
  if (timeoutCreateTime) {
    var dateText = orderDate ? DOMUtils.text(orderDate) || formatDate(new Date().getTime()) : formatDate(new Date().getTime());
    DOMUtils.text(timeoutCreateTime, dateText);
  }
}
function qrcode_timeout(msg) {
  showTimeoutState({
    message: msg || '',
    orderData: window.currentOrderData || null
  });
  fillTimeoutOrderInfo();
  var timeoutMessage = $('timeoutMessage');
  timeoutMessage && DOMUtils.html(timeoutMessage, msg || '');
}
var getQueryString = function getQueryString(name) {
  var regex = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i"),
    result = regex.exec(window.location.search.substr(1));
  return result != null ? decodeURI(result[2]) : null;
};
var PaymentModule = {
  nowPayType: -1,
  wxUrl: "",
  zfbUrl: "",
  otherUrl: "",
  orderFrom: 0,
  isLoading: false,
  switchPayType: function switchPayType(payType) {
    if (this.isLoading) {
      MessageModule.warning('正在处理中，请稍候...', 1500);
      return;
    }
    var url = window.location.href,
      isAlipay = payType == 0 || payType == 2;
    url = this.orderFrom != 0 ? this.otherUrl : isAlipay ? this.zfbUrl : this.wxUrl;
    if (!url) url = window.location.href;
    this.updatePaymentUI(payType, isAlipay);
    this.updateQRCode(url);
    this.changePayType(payType);
  },
  updatePaymentUI: function updatePaymentUI(payType, isAlipay) {
    var alipayLi = $('alipayLi'),
      wxpayLi = $('wxpayLi'),
      payTypeMsg = $('payTypeMsg'),
      countdownSection = $('.countdown-section');
    DOMUtils.removeClass(alipayLi, 'pay_clickli');
    DOMUtils.removeClass(wxpayLi, 'pay_clickli');
    countdownSection && (DOMUtils.removeClass(countdownSection, 'alipay'), DOMUtils.removeClass(countdownSection, 'wechat'));
    if (isAlipay) {
      DOMUtils.addClass(alipayLi, 'pay_clickli');
      DOMUtils.text(payTypeMsg, '支付宝');
      countdownSection && DOMUtils.addClass(countdownSection, 'alipay');
    } else {
      DOMUtils.addClass(wxpayLi, 'pay_clickli');
      DOMUtils.text(payTypeMsg, '微信支付');
      countdownSection && DOMUtils.addClass(countdownSection, 'wechat');
    }
    this.addSwitchAnimation(isAlipay ? alipayLi : wxpayLi);
  },
  addSwitchAnimation: function addSwitchAnimation(element) {
    if (!element) return;
    element.style.cssText = 'transform:scale(.95);transition:transform .2s ease';
    setTimeout(function () {
      return element.style.transform = 'scale(1)';
    }, 100);
  },
  initCountdownStyle: function initCountdownStyle(payType) {
    var countdownSection = $('.countdown-section');
    if (!countdownSection) return;
    DOMUtils.removeClass(countdownSection, 'alipay');
    DOMUtils.removeClass(countdownSection, 'wechat');
    var isAlipay = payType == 0 || payType == 2;
    DOMUtils.addClass(countdownSection, isAlipay ? 'alipay' : 'wechat');
  },
  updateQRCode: function updateQRCode(url) {
    var _this = this;
    var qrcodeEl = $('qrcode');
    if (!qrcodeEl) return;
    DOMUtils.html(qrcodeEl, "");
    this.showQRCodeLoading(qrcodeEl);
    if (url.indexOf("/static") == 0) {
      var img = document.createElement('img');
      img.src = url;
      img.alt = '支付二维码';
      img.style.cssText = 'max-width:100%;height:auto';
      img.onload = function () {
        _this.hideQRCodeLoading();
        DOMUtils.html(qrcodeEl, "");
        qrcodeEl.appendChild(img);
      };
      img.onerror = function () {
        return PaymentModule.showQRCodeError(qrcodeEl);
      };
    } else {
      try {
        var canvas = QRCode.generate(url, {
          width: CONFIG.QRCODE_SIZE,
          height: CONFIG.QRCODE_SIZE,
          foreground: "#000000",
          background: "#ffffff",
          borderRadius: 8
        });
        this.hideQRCodeLoading();
        DOMUtils.html(qrcodeEl, "");
        qrcodeEl.appendChild(canvas);
      } catch (error) {
        PaymentModule.showQRCodeError(qrcodeEl);
      }
    }
  },
  showQRCodeLoading: function showQRCodeLoading(container) {
    var existingLoading = container.querySelector('.qrcode-loading');
    existingLoading && existingLoading.parentNode && existingLoading.parentNode.removeChild(existingLoading);
    var loadingEl = document.createElement('div');
    loadingEl.className = 'qrcode-loading';
    loadingEl.innerHTML = '<div class="loading-spinner"></div><div class="loading-text">正在加载支付信息...</div>';
    DOMUtils.css(loadingEl, {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: CONFIG.QRCODE_SIZE + 'px',
      color: '#6b7280',
      background: '#f9fafb',
      borderRadius: '8px'
    });
    container.appendChild(loadingEl);
  },
  hideQRCodeLoading: function hideQRCodeLoading() {
    var loadingEl = $('.qrcode-loading');
    loadingEl && loadingEl.parentNode && loadingEl.parentNode.removeChild(loadingEl);
  },
  showQRCodeError: function showQRCodeError(container) {
    this.hideQRCodeLoading();
    var errorEl = document.createElement('div');
    errorEl.className = 'qrcode-error';
    errorEl.innerHTML = '<div class="error-icon">⚠️</div><div class="error-text">二维码生成失败</div><button class="retry-btn" onclick="PaymentModule.retryQRCode()">重试</button>';
    DOMUtils.css(errorEl, {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: CONFIG.QRCODE_SIZE + 'px',
      color: '#999',
      textAlign: 'center'
    });
    container.appendChild(errorEl);
  },
  retryQRCode: function retryQRCode() {
    this.updateQRCode(this.getCurrentPayUrl());
  },
  getCurrentPayUrl: function getCurrentPayUrl() {
    var isAlipay = this.nowPayType == 0 || this.nowPayType == 2;
    return this.orderFrom != 0 ? this.otherUrl : isAlipay ? this.zfbUrl : this.wxUrl;
  },
  changePayType: function changePayType(payType) {
    var _this2 = this;
    if (this.nowPayType == payType) {
      var payTypeName = this.orderFrom == 1 ? "支付宝/微信" : (payType == 0 ? "支付宝" : "微信支付");
      var tipMessage = this.orderFrom == 1 ? "请使用【" + payTypeName + "】扫码支付！" : "请使用【" + payTypeName + "】扫码支付！<br>也可以在顶部切换其他付款方式！";
      MessageModule.success(tipMessage, 2000);
      return;
    }
    this.isLoading = true;
    this.nowPayType = payType;
    var orderId = getQueryString("orderId");
    AjaxUtils.post("/code.aspx?op=changePayType", "orderId=" + orderId + "&payType=" + payType, function (response) {
      _this2.isLoading = false;
      _this2.handlePayTypeChangeSuccess(payType);
    }, function (error) {
      _this2.isLoading = false;
      _this2.handlePayTypeChangeError(payType, error);
    });
  },
  handlePayTypeChangeSuccess: function handlePayTypeChangeSuccess(payType) {
    var payTypeName = payType == 0 ? "支付宝" : "微信支付";
    if (navigator.userAgent.match(/MicroMessenger\//i) && payType == 1) MessageModule.success("支付方式变更为【微信支付】，请重新扫码！", 3000);else MessageModule.success("请使用【" + payTypeName + "】扫码支付！<br>也可以在顶部切换其他付款方式！", 2000);
  },
  handlePayTypeChangeError: function handlePayTypeChangeError(payType, error) {
    MessageModule.error("切换支付方式失败，请重试", 3000);
  },
  timeout: function timeout(msg) {
    qrcode_timeout(msg);
  }
};
var changePay = function changePay(payType) {
  return PaymentModule.switchPayType(payType);
};
function showDetail() {
  var orderDetail = $('orderDetail'),
    detailContent = $('orderDetail').querySelector('.detail-ct'),
    arrow = orderDetail ? orderDetail.querySelector('.ico-arrow') : null,
    arrowText = orderDetail ? orderDetail.querySelector('.arrow-text') : null;
  if (DOMUtils.hasClass(orderDetail, 'detail-open')) {
    DOMUtils.hide(detailContent);
    DOMUtils.removeClass(orderDetail, 'detail-open');
    arrowText && (arrowText.textContent = '查看订单详情');
    arrow && (arrow.style.transform = 'rotate(45deg)');
  } else {
    DOMUtils.show(detailContent);
    DOMUtils.addClass(orderDetail, 'detail-open');
    arrowText && (arrowText.textContent = '收起订单详情');
    arrow && (arrow.style.transform = 'rotate(225deg)');
  }
}
PaymentModule.initOrder = function () {
  var _this3 = this;
  var orderId = getQueryString("orderId");
  if (!orderId) {
    this.handleOrderError("订单ID不存在");
    return;
  }
  AjaxUtils.post("/code.aspx?op=getOrder", "orderId=" + orderId, function (data) {
    return data.code == 1 ? _this3.handleOrderData(data.data) : _this3.handleOrderError(data.msg || "获取订单信息失败");
  }, function (error) {
    return _this3.handleOrderError("网络错误，请检查网络连接");
  });
};
PaymentModule.handleOrderData = function (orderData) {
  var timeLeft = 0;
  window.currentOrderData = orderData;
  if (orderData.state >= 1) {
    this.handlePaymentSuccess(orderData);
    return;
  }
  if (orderData.state != -1) {
    var elapsed = (new Date().getTime() - orderData.date) / 1000;
    timeLeft = Math.max(0, orderData.timeOut * 60 - elapsed);
  }
  this.zfbUrl = orderData.zfbPayUrl;
  this.wxUrl = orderData.wxPayUrl;
  this.otherUrl = orderData.payUrl;
  this.orderFrom = orderData.from;
  if (this.orderFrom == 1) {
    DOMUtils.hide($('.mod-title'));
  }
  if (this.handleSpecialBrowser(orderData)) return;
  this.updateOrderInfo(orderData);
  if (timeLeft <= 0) {
    showTimeoutState({
      message: '',
      orderData: orderData
    });
    return;
  }
  showNormalState({
    timeLeft: timeLeft,
    needUserPay: orderData.needUserPay,
    orderData: orderData
  });
  this.switchPayType(orderData.payType);
  window.intDiff = timeLeft;
  TimerModule.start();
  this.check();
  orderData.qq && this.setupCustomerService(orderData.qq);
};
PaymentModule.handlePaymentSuccess = function (orderData) {
  showSuccessState({
    orderData: orderData,
    returnUrl: orderData.returnUrl
  });
};
PaymentModule.handleSpecialBrowser = function (orderData) {
  if (navigator.userAgent.match(/Alipay/i) && this.zfbUrl) {
    window.location.href = this.zfbUrl;
    return true;
  }
  if (navigator.userAgent.match(/MicroMessenger\//i) && this.wxUrl && orderData.payType != 1) {
    orderData.payType = 1;
    this.changePayType(orderData.payType);
    return true;
  }
  return false;
};
PaymentModule.updateOrderInfo = function (orderData) {
  var updates = {
    strRemark: orderData.remark,
    money: "￥" + orderData.reallyPrice.toFixed(2),
    strPrice: orderData.price,
    strPayId: orderData.payId,
    strOrderId: orderData.orderId,
    strDate: formatDate(orderData.date)
  };
  Object.keys(updates).forEach(function (id) {
    var el = $(id);
    el && DOMUtils.text(el, updates[id]);
  });
  this.nowPayType = orderData.payType;
  this.initCountdownStyle(orderData.payType);
  orderData.needUserPay && DOMUtils.show($('lblYouHui'));
};
PaymentModule.setupCustomerService = function (qq) {
  var qqHead = $('qqHead'),
    productIcon = $('productIcon'),
    keFuQQ = $('keFuQQ'),
    keFuQQ1 = $('keFuQQ1'),
    logoUrl = "https://q1.qlogo.cn/g?b=qq&nk=" + qq + "&s=100",
    qqUrl = "http://wpa.qq.com/msgrd?v=3&uin=" + qq + "&site=qq&menu=yes";
  qqHead && (DOMUtils.attr(qqHead, 'src', logoUrl), DOMUtils.show(qqHead));
  productIcon && (DOMUtils.attr(productIcon, 'src', logoUrl), DOMUtils.show(productIcon));
  keFuQQ && DOMUtils.attr(keFuQQ, 'href', qqUrl);
  keFuQQ1 && DOMUtils.attr(keFuQQ1, 'href', qqUrl);
  window.customerQQ = qq;
};
PaymentModule.handleOrderError = function (errorMsg) {
  showTimeoutState({
    message: errorMsg,
    orderData: null
  });
  MessageModule.error(errorMsg, 5000);
};
PaymentModule.check = function () {
  var _this4 = this;
  var orderId = getQueryString("orderId");
  if (!orderId) return;
  AjaxUtils.post("/code.aspx?op=checkOrder", "orderId=" + orderId, function (data) {
    if (data.code == 1) showSuccessState({
      returnUrl: data.data
    });else {
      data.date = data.date == null || data.date < 0 ? 0 : data.date;
      window.intDiff = Math.max(0, data.date);
      if (window.intDiff != 0 && data.payType != null && _this4.nowPayType != data.payType) _this4.switchPayType(data.payType);
    }
  }, function (error) {});
};
showLoadingState();
PaymentModule.initOrder();
    </script>
</body>
</html>
