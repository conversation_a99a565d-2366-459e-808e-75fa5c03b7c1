using Account.Web.Common.Translate;
using Account.Web.LanguageDetection;
using CommonLib;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;

namespace Account.Web
{
    /// <summary>
    /// 处理多语言URL路径的HttpModule
    /// 统一的语言检测和URL处理入口，实现最小化重定向策略
    /// </summary>
    public class LanguagePathModule : IHttpModule
    {
        private static readonly List<string> BackgroundPages = new List<string>() {
            // 用户管理
            "alluser.aspx", "newuser.aspx", "iplist.aspx",
            // 系统管理  
            "code.aspx", "mail.aspx", "paylist.aspx",
            // 翻译管理
            "translationadmin.aspx",
            // 文章管理
            "articleeditor.aspx", "articlemanager.aspx", "categoryeditor.aspx", "categorymanager.aspx",
            // 特殊标识
            "yandex"
        };

        private static readonly List<string> lstExpBackgroundPages = new List<string>() {
            "encode.aspx"
        };

        public static bool IsBackGroundPage(string path)
        {
            bool result = false;
            if (path.Contains(".ashx") ||
                (BackgroundPages.Any(p => path.IndexOf(p, StringComparison.OrdinalIgnoreCase) >= 0) && !lstExpBackgroundPages.Any(p => path.IndexOf(p, StringComparison.OrdinalIgnoreCase) >= 0))
                )
            {
                return true;
            }

            return result;
        }


        public void Init(HttpApplication application)
        {
            application.BeginRequest += Application_BeginRequest;
        }

        /// <summary>
        /// 统一的语言检测入口
        /// 实现按优先级的语言检测和一次性重定向逻辑
        /// </summary>
        void Application_BeginRequest(object sender, EventArgs e)
        {
            HttpContext context = ((HttpApplication)sender).Context;

            if (context.Request.Path.StartsWith("/signalr", StringComparison.OrdinalIgnoreCase))
            {
                return;
            }

            // 初始化性能监控和调试信息
            var debugInfo = InitializeEnhancedDebugInfo(context);

            try
            {
                debugInfo.AddDetectionStep(LanguageSource.Default, true, false, null, null, "开始语言处理流程");

                // 1. 统一语言检测（带性能监控）
                var detectionTimer = debugInfo.StartTiming("语言检测");
                var languageInfo = DetectLanguageFromAllSources(context, debugInfo);
                debugInfo.SetLanguageDetectionTime(detectionTimer.Elapsed);
                detectionTimer.Stop();

                debugInfo.DetectedLanguage = languageInfo;

                // 2. 确定最终URL（带性能监控）
                var urlTimer = debugInfo.StartTiming("URL处理");
                var finalUrl = DetermineFinalUrl(context, languageInfo, debugInfo);
                debugInfo.SetUrlProcessingTime(urlTimer.Elapsed);
                urlTimer.Stop();

                // 3. 执行重定向（如果需要）
                if (!string.IsNullOrEmpty(finalUrl))
                {
                    var redirectTimer = debugInfo.StartTiming("重定向执行");
                    ExecuteRedirect(context, finalUrl, languageInfo, debugInfo);
                    debugInfo.SetRedirectExecutionTime(redirectTimer.Elapsed);
                    redirectTimer.Stop();

                    debugInfo.CompleteProcessing();
                    return;
                }

                // 4. 设置上下文并重写路径
                SetLanguageContextAndRewritePath(context, languageInfo, debugInfo);

                debugInfo.CompleteProcessing();
            }
            catch (Exception ex)
            {
                // 降级处理：使用默认语言
                debugInfo.AddError("Application_BeginRequest", ex, "语言检测过程中发生异常");

                HandleLanguageDetectionError(context, ex, debugInfo);

                debugInfo.CompleteProcessing();
            }
        }

        /// <summary>
        /// 初始化增强的调试信息
        /// </summary>
        private LanguageDebugInfo InitializeEnhancedDebugInfo(HttpContext context)
        {
            var debugInfo = new LanguageDebugInfo(
                context.Request.Url?.ToString(),
                context.Request.Path,
                context.Request.QueryString.ToString()
            );

            // 保存到上下文中，供其他组件使用
            context.Items["LanguageDebugInfo"] = debugInfo;

            return debugInfo;
        }

        /// <summary>
        /// 统一语言检测入口 - 使用新的统一检测器
        /// 优先级顺序：
        /// 1. URL路径 - 如 /zh-Hans/page.aspx
        /// 2. 查询参数 - 如 ?lang=zh-Hans
        /// 3. Cookie - 用户保存的语言偏好
        /// 4. 浏览器头 - Accept-Language头
        /// 5. 默认语言 - 最后的回退选项
        /// </summary>
        private LanguageInfo DetectLanguageFromAllSources(HttpContext context, LanguageDebugInfo debugInfo)
        {
            try
            {
                // 使用统一语言检测器
                var languageInfo = UnifiedLanguageDetector.DetectLanguage(context, debugInfo, true);

                return languageInfo;
            }
            catch (Exception ex)
            {
                // 返回默认语言作为降级处理
                debugInfo.AddDetectionStep(LanguageSource.Default, true, true, LanguageConfiguration.DefaultLanguage, LanguageConfiguration.DefaultLanguage, "异常降级使用默认语言");
                return LanguageInfo.CreateDefault();
            }
        }

        /// <summary>
        /// 智能重定向决策器
        /// 根据需求1.1、2.1、3.1实现智能重定向决策
        /// 任务7.2: 添加监控和日志记录
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <param name="languageInfo">检测到的语言信息</param>
        /// <param name="debugInfo">调试信息</param>
        /// <returns>重定向决策结果</returns>
        private RedirectDecision DetermineSmartRedirect(HttpContext context, LanguageInfo languageInfo, LanguageDebugInfo debugInfo)
        {
            UrlFormatInfo urlInfo = null;
            RedirectDecision decision = null;

            try
            {
                // 解析URL格式
                urlInfo = UrlFormatParser.ParseUrlFormat(context.Request.Path);

                // 添加调试信息
                debugInfo?.AddWarning($"URL格式解析: {urlInfo.ToString()}");

                // 使用智能重定向决策引擎
                decision = SmartRedirectDecisionEngine.DetermineSmartRedirect(context, urlInfo, languageInfo, debugInfo);

                // 验证决策有效性
                if (!SmartRedirectDecisionEngine.ValidateDecision(decision, context.Request.Path))
                {
                    debugInfo?.AddWarning($"重定向决策验证失败: {decision?.ToString()}");

                    return RedirectDecision.NoAction("决策验证失败");
                }

                // 记录决策过程
                debugInfo?.AddWarning($"智能重定向决策: {decision?.GetDetailedInfo()}");

                return decision;
            }
            catch (Exception ex)
            {
                debugInfo?.AddError("DetermineSmartRedirect", ex, "智能重定向决策异常");

                return RedirectDecision.NoAction($"决策异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 确定最终URL，统一URL标准化逻辑
        /// 实现一次性重定向逻辑，满足需求2.1-2.5
        /// 已集成智能重定向决策器
        /// 任务7.2: 添加URL处理监控
        /// </summary>
        private string DetermineFinalUrl(HttpContext context, LanguageInfo languageInfo, LanguageDebugInfo debugInfo)
        {
            string path = context.Request.Path;
            string queryString = context.Request.QueryString.ToString();
            try
            {
                // 输入验证
                if (string.IsNullOrEmpty(path) || languageInfo == null || string.IsNullOrEmpty(languageInfo.StandardCode))
                {
                    debugInfo.AddWarning("URL处理输入参数无效");
                    return null;
                }

                // 检查是否为后台页面，如果是则不进行重定向
                if (IsBackGroundPage(path))
                {
                    debugInfo.SetRedirectInfo(false, null, "后台页面跳过重定向", "无");
                    return null; // 无需重定向
                }

                // 使用智能重定向决策器
                var smartDecision = DetermineSmartRedirect(context, languageInfo, debugInfo);

                // 处理智能决策结果
                if (smartDecision != null && smartDecision.RequiresAction)
                {
                    if (smartDecision.IsRedirect)
                    {
                        // 需要重定向，处理查询参数
                        string finalUrl = smartDecision.TargetUrl;
                        finalUrl = AppendCleanQueryString(finalUrl, queryString);

                        // 设置重定向信息
                        debugInfo.SetRedirectInfo(true, finalUrl, smartDecision.Reason, smartDecision.StatusCode.ToString());
                        return finalUrl;
                    }
                    else if (smartDecision.IsUrlRewrite)
                    {
                        // URL重写情况，不返回重定向URL，而是在后续处理中进行重写
                        debugInfo.SetRedirectInfo(false, null, smartDecision.Reason, "URL重写");

                        // 保存重写信息到上下文，供后续处理使用
                        context.Items["SmartRedirectDecision"] = smartDecision;
                        return null; // 无需重定向，将通过URL重写处理
                    }
                    else if (smartDecision.IsDirectProcess)
                    {
                        // 直接处理，无需重定向
                        debugInfo.SetRedirectInfo(false, null, smartDecision.Reason, "直接处理");
                        return null;
                    }
                }

                // 回退到原有逻辑（保持兼容性）
                debugInfo.AddWarning("使用回退逻辑处理URL");
                return DetermineFinalUrlFallback(context, languageInfo, debugInfo, queryString);
            }
            catch (Exception ex)
            {
                debugInfo.AddError("DetermineFinalUrl", ex, $"确定最终URL时发生异常: {path}");
                // 异常时回退到原有逻辑
                try
                {
                    return DetermineFinalUrlFallback(context, languageInfo, debugInfo, queryString);
                }
                catch (Exception fallbackEx)
                {
                    debugInfo.AddError("DetermineFinalUrlFallback", fallbackEx, "回退逻辑也发生异常");
                    return null;
                }
            }
        }

        /// <summary>
        /// 回退逻辑：保持现有重定向场景的兼容性
        /// 根据需求1.1、2.1，保持现有重定向场景的兼容性
        /// </summary>
        private string DetermineFinalUrlFallback(HttpContext context, LanguageInfo languageInfo, LanguageDebugInfo debugInfo, string queryString)
        {
            string path = context.Request.Path;

            // 需求2.1: 处理根路径 "/" -> "/zh-Hans/" (1次重定向)
            if (path == "/")
            {
                string finalUrl = $"/{languageInfo.StandardCode}/";
                finalUrl = AppendCleanQueryString(finalUrl, queryString);
                debugInfo.SetRedirectInfo(true, finalUrl, "根路径重定向（回退逻辑）", "301");
                return finalUrl;
            }

            // 需求2.2: 处理语言别名 "/zh" -> "/zh-Hans/" (1次重定向)
            if (IsLanguageAlias(path, languageInfo))
            {
                string finalUrl = $"/{languageInfo.StandardCode}/";
                finalUrl = AppendCleanQueryString(finalUrl, queryString);
                debugInfo.SetRedirectInfo(true, finalUrl, "语言别名重定向（回退逻辑）", "301");
                return finalUrl;
            }

            // 需求2.3: 处理目录路径斜杠补全 "/zh-Hans" -> "/zh-Hans/" (1次重定向)
            if (IsDirectoryWithoutSlash(context, path, languageInfo))
            {
                string finalUrl = path + "/";
                finalUrl = AppendCleanQueryString(finalUrl, queryString);
                debugInfo.SetRedirectInfo(true, finalUrl, "目录路径斜杠补全（回退逻辑）", "301");
                return finalUrl;
            }

            // 需求2.4: 处理查询参数中的语言 "/default.aspx?lang=zh&other=value" -> "/zh-Hans/default.aspx?other=value" (1次重定向)
            if (HasLanguageInQueryString(context) && languageInfo.Source == LanguageSource.QueryString)
            {
                string finalUrl = BuildUrlWithLanguagePrefix(path, languageInfo.StandardCode);
                finalUrl = AppendCleanQueryString(finalUrl, queryString);
                debugInfo.SetRedirectInfo(true, finalUrl, "查询参数语言重定向（回退逻辑）", "301");
                return finalUrl;
            }

            // 需求2.5: 检查是否已经是标准格式，如果是则不执行重定向
            if (IsStandardFormat(path, languageInfo.StandardCode))
            {
                debugInfo.SetRedirectInfo(false, null, "已是标准格式（回退逻辑）", "无");
                return null; // 无需重定向
            }

            // 检查是否为需要URL重写而非重定向的情况
            if (ShouldUseUrlRewriteInsteadOfRedirect(path, languageInfo))
            {
                debugInfo.SetRedirectInfo(false, null, "使用URL重写而非重定向（回退逻辑）", "无");
                return null; // 无需重定向，将通过URL重写处理
            }

            // 其他情况：需要添加语言前缀的URL（如Cookie、浏览器头、默认语言来源）
            if (!path.StartsWith($"/{languageInfo.StandardCode}/", StringComparison.OrdinalIgnoreCase))
            {
                string finalUrl = BuildUrlWithLanguagePrefix(path, languageInfo.StandardCode);
                finalUrl = AppendCleanQueryString(finalUrl, queryString);
                debugInfo.SetRedirectInfo(true, finalUrl, "添加语言前缀（回退逻辑）", "301");
                return finalUrl;
            }

            debugInfo.SetRedirectInfo(false, null, "无需重定向（回退逻辑）", "无");
            return null; // 无需重定向
        }

        /// <summary>
        /// 检查是否为语言别名
        /// 需求2.2: 检测如"/zh"这样的语言别名，需要重定向到标准格式"/zh-Hans/"
        /// </summary>
        private bool IsLanguageAlias(string path, LanguageInfo languageInfo)
        {
            if (languageInfo.Source != LanguageSource.UrlPath)
                return false;

            // 检查是否为语言别名（原始输入与标准代码不同）
            bool isAlias = !string.Equals(languageInfo.OriginalInput, languageInfo.StandardCode, StringComparison.OrdinalIgnoreCase);

            // 同时检查路径格式是否为纯语言代码格式（如"/zh"或"/zh/"）
            string normalizedPath = path.TrimEnd('/');
            bool isPureLanguagePath = normalizedPath == $"/{languageInfo.OriginalInput}";

            return isAlias && isPureLanguagePath;
        }

        /// <summary>
        /// 检查是否为语言根目录且缺少斜杠
        /// 例如："/zh-Hans" 这种情况应该直接重写而不是重定向，避免相对路径问题
        /// </summary>
        private bool IsLanguageRootWithoutSlash(string path, LanguageInfo languageInfo)
        {
            if (string.IsNullOrEmpty(path) || path.EndsWith("/") || languageInfo?.Source != LanguageSource.UrlPath)
                return false;

            // 检查是否为标准语言根目录路径（如"/zh-Hans"）
            string expectedLanguagePath = $"/{languageInfo.StandardCode}";
            return string.Equals(path, expectedLanguagePath, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 检查是否为缺少斜杠的目录路径
        /// 需求2.3: 检测如"/zh-Hans"这样缺少尾部斜杠的目录路径，需要重定向到"/zh-Hans/"
        /// </summary>
        private bool IsDirectoryWithoutSlash(HttpContext context, string path, LanguageInfo languageInfo)
        {
            if (string.IsNullOrEmpty(path) || path.EndsWith("/"))
                return false;

            // 检查是否为语言目录路径（如"/zh-Hans"）
            if (languageInfo.Source == LanguageSource.UrlPath)
            {
                string expectedLanguagePath = $"/{languageInfo.StandardCode}";
                if (string.Equals(path, expectedLanguagePath, StringComparison.OrdinalIgnoreCase))
                {
                    return true; // 这是一个语言目录路径，需要补斜杠
                }

                // 也检查原始输入的语言路径（如"/zh"对应"/zh-Hans"）
                string originalLanguagePath = $"/{languageInfo.OriginalInput}";
                if (string.Equals(path, originalLanguagePath, StringComparison.OrdinalIgnoreCase))
                {
                    return false; // 这种情况应该由IsLanguageAlias处理，不是目录斜杠补全
                }
            }

            // 检查是否为其他目录路径
            try
            {
                return UrlService.IsDirectory("~" + path);
            }
            catch (Exception)
            {
                // 如果检查目录时出错，保守处理，认为不是目录
                return false;
            }
        }

        /// <summary>
        /// 检查查询字符串中是否包含语言参数
        /// </summary>
        private bool HasLanguageInQueryString(HttpContext context)
        {
            return !string.IsNullOrEmpty(context.Request.QueryString["lang"]);
        }

        /// <summary>
        /// 从查询字符串中移除lang参数
        /// </summary>
        private string RemoveLangFromQueryString(string queryString)
        {
            if (string.IsNullOrEmpty(queryString))
                return string.Empty;

            try
            {
                var queryParams = HttpUtility.ParseQueryString(queryString);
                queryParams.Remove("lang");

                // 移除所有可能的lang参数变体（防止大小写问题）
                queryParams.Remove("Lang");
                queryParams.Remove("LANG");

                return queryParams.ToString();
            }
            catch (Exception)
            {
                // 如果解析查询字符串失败，尝试简单的字符串替换
                return RemoveLangParameterByStringReplacement(queryString);
            }
        }

        /// <summary>
        /// 通过字符串替换的方式移除lang参数（备用方法）
        /// </summary>
        private string RemoveLangParameterByStringReplacement(string queryString)
        {
            if (string.IsNullOrEmpty(queryString))
                return string.Empty;

            try
            {
                // 移除各种可能的lang参数格式
                var patterns = new[] {
                    @"[&?]lang=[^&]*",
                    @"[&?]Lang=[^&]*",
                    @"[&?]LANG=[^&]*"
                };

                foreach (var pattern in patterns)
                {
                    queryString = System.Text.RegularExpressions.Regex.Replace(queryString, pattern, "",
                        System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                }

                // 清理开头的&符号
                queryString = queryString.TrimStart('&');

                return queryString;
            }
            catch (Exception)
            {
                // 最后的保护措施，返回空字符串
                return string.Empty;
            }
        }

        /// <summary>
        /// 构建带语言前缀的URL
        /// 需求2.4: 正确处理各种路径格式，添加语言前缀
        /// </summary>
        private string BuildUrlWithLanguagePrefix(string path, string languageCode)
        {
            if (string.IsNullOrEmpty(path) || path == "/")
            {
                return $"/{languageCode}/";
            }

            // 确保路径以"/"开头
            if (!path.StartsWith("/"))
            {
                path = "/" + path;
            }

            // 检查是否已经包含语言前缀，避免重复添加
            if (path.StartsWith($"/{languageCode}/", StringComparison.OrdinalIgnoreCase))
            {
                return path; // 已经包含正确的语言前缀
            }

            // 构建带语言前缀的URL
            return $"/{languageCode}{path}";
        }

        /// <summary>
        /// 附加清理后的查询字符串到URL
        /// 需求2.4: 正确处理和保留查询参数，移除lang参数
        /// </summary>
        private string AppendCleanQueryString(string url, string queryString)
        {
            if (string.IsNullOrEmpty(queryString))
                return url;

            try
            {
                // 移除lang参数，保留其他参数
                string cleanQueryString = RemoveLangFromQueryString(queryString);

                if (!string.IsNullOrEmpty(cleanQueryString))
                {
                    return url + "?" + cleanQueryString;
                }
            }
            catch (Exception)
            {
                // 如果查询字符串处理出错，返回不带查询参数的URL
                // 这样可以确保重定向仍然能够进行，只是丢失了查询参数
            }

            return url;
        }

        /// <summary>
        /// 检查是否已经是标准格式
        /// 需求2.5: 确保标准格式URL不执行重定向
        /// </summary>
        private bool IsStandardFormat(string path, string standardLanguageCode)
        {
            if (string.IsNullOrEmpty(path) || string.IsNullOrEmpty(standardLanguageCode))
                return false;

            // 检查是否以标准语言代码开头的路径格式
            return path.StartsWith($"/{standardLanguageCode}/", StringComparison.OrdinalIgnoreCase) ||
                   string.Equals(path, $"/{standardLanguageCode}", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 检查是否应该使用URL重写而不是重定向
        /// 当用户直接访问带语言前缀的URL时，应该重写而不是重定向
        /// </summary>
        /// <param name="path">请求路径</param>
        /// <param name="languageInfo">语言信息</param>
        /// <returns>如果应该使用URL重写返回true，否则返回false</returns>
        private bool ShouldUseUrlRewriteInsteadOfRedirect(string path, LanguageInfo languageInfo)
        {
            if (string.IsNullOrEmpty(path) || languageInfo == null || string.IsNullOrEmpty(languageInfo.StandardCode))
                return false;

            // 如果语言来源是URL路径，且路径已经包含正确的语言前缀，则使用重写
            if (languageInfo.Source == LanguageSource.UrlPath)
            {
                string languagePrefix = $"/{languageInfo.StandardCode}/";
                string languagePrefixNoSlash = $"/{languageInfo.StandardCode}";

                // 检查是否为标准格式的语言URL
                if (path.StartsWith(languagePrefix, StringComparison.OrdinalIgnoreCase) ||
                    path.Equals(languagePrefixNoSlash, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 执行重定向
        /// 实现一次性重定向逻辑，确保只执行一次重定向
        /// </summary>
        private void ExecuteRedirect(HttpContext context, string finalUrl, LanguageInfo languageInfo, LanguageDebugInfo debugInfo)
        {
            try
            {
                // 增强的重定向循环检测
                var loopDetectionResult = RedirectLoopDetector.DetectLoop(context, finalUrl);
                if (loopDetectionResult.HasLoop)
                {
                    debugInfo.SetRedirectLoopDetection(true, loopDetectionResult.Details);
                    debugInfo.AddWarning($"重定向循环检测: {loopDetectionResult.Reason}");

                    // 循环检测时继续正常处理，设置语言上下文
                    SetLanguageContextAndRewritePath(context, languageInfo, debugInfo);
                    return;
                }

                // 验证目标URL的有效性
                if (!IsValidRedirectUrl(finalUrl))
                {
                    debugInfo.AddWarning($"目标URL无效: {finalUrl}");
                    SetLanguageContextAndRewritePath(context, languageInfo, debugInfo);
                    return;
                }

                // 在重定向前设置语言Cookie，确保新页面能正确检测语言
                SetLanguageCookie(context, languageInfo, debugInfo);

                // 更新调试信息
                debugInfo.SetRedirectInfo(true, finalUrl, GetRedirectReason(languageInfo), "301");

                // 使用301永久重定向，对SEO友好
                context.Response.RedirectPermanent(finalUrl);
                context.Response.End();
            }
            catch (Exception ex)
            {
                debugInfo.AddError("ExecuteRedirect", ex, $"执行重定向时发生异常: {finalUrl}");

                // 重定向失败时继续正常处理，设置语言上下文
                SetLanguageContextAndRewritePath(context, languageInfo, debugInfo);
            }
        }

        /// <summary>
        /// 设置语言上下文并重写路径
        /// 增强版本：支持URL格式信息和SEO标签生成
        /// </summary>
        private void SetLanguageContextAndRewritePath(HttpContext context, LanguageInfo languageInfo, LanguageDebugInfo debugInfo)
        {
            try
            {
                // 1. 获取或解析URL格式信息
                UrlFormatInfo urlInfo = GetOrParseUrlFormatInfo(context, debugInfo);

                // 2. 设置基础语言上下文信息
                context.Items["lang"] = languageInfo.StandardCode;
                context.Items["LanguageInfo"] = languageInfo;
                context.Items["UrlFormat"] = urlInfo;



                // 4. 设置语言Cookie，供translate.js等前端脚本使用
                SetLanguageCookie(context, languageInfo, debugInfo);

                // 5. 执行URL重写逻辑
                PerformUrlRewrite(context, languageInfo, debugInfo);

                debugInfo.AddDetectionStep(LanguageSource.Default, true, true, null, null, $"已设置语言上下文: {languageInfo.StandardCode}, URL格式: {urlInfo.Type}");
            }
            catch (Exception ex)
            {
                debugInfo.AddError("SetLanguageContextAndRewritePath", ex, "设置语言上下文时发生异常");
            }
        }

        /// <summary>
        /// 获取或解析URL格式信息
        /// 优先从上下文获取，如果不存在则重新解析
        /// </summary>
        private UrlFormatInfo GetOrParseUrlFormatInfo(HttpContext context, LanguageDebugInfo debugInfo)
        {
            try
            {
                // 1. 尝试从上下文获取已解析的URL格式信息
                if (context.Items["UrlFormat"] is UrlFormatInfo existingUrlInfo)
                {
                    debugInfo?.AddWarning($"从上下文获取URL格式信息: {existingUrlInfo.Type}");
                    return existingUrlInfo;
                }

                // 2. 尝试从SmartRedirectDecision获取（注意：RedirectDecision不包含UrlInfo，跳过此步骤）
                // 如果需要从决策中获取URL信息，需要重新设计数据结构

                // 3. 重新解析URL格式
                var urlInfo = UrlFormatParser.ParseUrlFormat(context.Request.Path);
                debugInfo?.AddWarning($"重新解析URL格式信息: {urlInfo.Type}");
                return urlInfo;
            }
            catch (Exception ex)
            {
                debugInfo?.AddError("GetOrParseUrlFormatInfo", ex, "获取URL格式信息时发生异常");
                // 返回默认的URL格式信息
                return new UrlFormatInfo(context.Request.Path);
            }
        }



        /// <summary>
        /// 设置语言Cookie
        /// 确保translate.js等前端脚本能正确检测到当前语言
        /// 增强版本：支持默认语言优化策略
        /// </summary>
        private void SetLanguageCookie(HttpContext context, LanguageInfo languageInfo, LanguageDebugInfo debugInfo)
        {
            try
            {
                // 检查当前Cookie值是否已经正确
                var currentCookie = context.Request.Cookies["lang"];
                string currentCookieValue = currentCookie?.Value?.Trim();

                // 如果Cookie值已经正确，无需重复设置
                if (string.Equals(currentCookieValue, languageInfo.StandardCode, StringComparison.OrdinalIgnoreCase))
                {
                    debugInfo.AddWarning($"语言Cookie已正确: {currentCookieValue}");
                    return;
                }

                // 特殊处理：确保默认语言访问时Cookie正确设置
                bool isDefaultLanguage = LanguageService.IsDefaultLanguage(languageInfo.StandardCode);
                if (isDefaultLanguage)
                {
                    debugInfo.AddWarning($"处理默认语言Cookie设置: {languageInfo.StandardCode}");
                }

                // 设置新的语言Cookie
                var langCookie = new HttpCookie("lang", languageInfo.StandardCode)
                {
                    Path = "/",
                    HttpOnly = false, // 允许JavaScript访问，供translate.js使用
                    Expires = DateTime.Now.AddYears(1), // 1年有效期
                    SameSite = SameSiteMode.Lax // 安全设置
                };

                context.Response.Cookies.Add(langCookie);

                // 验证Cookie设置是否成功
                ValidateCookieSetting(context, languageInfo, debugInfo);

                debugInfo.AddWarning($"已设置语言Cookie: {currentCookieValue} -> {languageInfo.StandardCode}");
                debugInfo.AddDetectionStep(LanguageSource.Default, true, true, currentCookieValue, languageInfo.StandardCode, "语言Cookie已更新");
            }
            catch (Exception ex)
            {
                debugInfo.AddError("SetLanguageCookie", ex, $"设置语言Cookie时发生异常: {languageInfo.StandardCode}");
            }
        }

        /// <summary>
        /// 验证Cookie设置是否成功
        /// 确保translate.js能正确检测语言
        /// </summary>
        private void ValidateCookieSetting(HttpContext context, LanguageInfo languageInfo, LanguageDebugInfo debugInfo)
        {
            try
            {
                // 检查Response中的Cookie是否正确设置
                var responseCookies = context.Response.Cookies;
                var langCookie = responseCookies["lang"];

                if (langCookie != null && langCookie.Value == languageInfo.StandardCode)
                {
                    debugInfo?.AddWarning($"Cookie设置验证成功: {langCookie.Value}");

                    // 添加额外的上下文信息，供translate.js使用
                    context.Items["CookieValidated"] = true;
                    context.Items["CookieLanguage"] = languageInfo.StandardCode;
                }
                else
                {
                    debugInfo?.AddWarning($"Cookie设置验证失败: 期望 {languageInfo.StandardCode}，实际 {langCookie?.Value}");
                }
            }
            catch (Exception ex)
            {
                debugInfo?.AddError("ValidateCookieSetting", ex, "验证Cookie设置时发生异常");
            }
        }

        /// <summary>
        /// 执行URL重写逻辑
        /// 将带语言前缀的URL重写为实际的物理路径
        /// 例如：/zh-Hans/default.aspx -> /default.aspx
        /// </summary>
        private void PerformUrlRewrite(HttpContext context, LanguageInfo languageInfo, LanguageDebugInfo debugInfo)
        {
            try
            {
                string originalPath = context.Request.Path;
                string rewrittenPath = null;

                // 首先检查是否有智能重定向决策的URL重写指令
                var smartDecision = context.Items["SmartRedirectDecision"] as RedirectDecision;
                if (smartDecision != null && smartDecision.IsUrlRewrite)
                {
                    rewrittenPath = smartDecision.TargetUrl;
                    debugInfo.AddWarning($"执行智能URL重写: {originalPath} -> {rewrittenPath} (原因: {smartDecision.Reason})");

                    // 执行URL重写
                    context.RewritePath(rewrittenPath, context.Request.PathInfo, context.Request.QueryString.ToString());

                    // 保存原始路径信息供其他组件使用
                    context.Items["OriginalPath"] = originalPath;
                    context.Items["RewrittenPath"] = rewrittenPath;
                    context.Items["SmartRewriteReason"] = smartDecision.Reason;

                    debugInfo.AddDetectionStep(LanguageSource.Default, true, true, originalPath, rewrittenPath, $"智能URL重写完成: {smartDecision.Reason}");
                    return;
                }

                // 回退到原有的URL重写逻辑
                if (ShouldPerformUrlRewrite(originalPath, languageInfo, out rewrittenPath))
                {
                    debugInfo.AddWarning($"执行传统URL重写: {originalPath} -> {rewrittenPath}");

                    // 执行URL重写
                    context.RewritePath(rewrittenPath, context.Request.PathInfo, context.Request.QueryString.ToString());

                    // 保存原始路径信息供其他组件使用
                    context.Items["OriginalPath"] = originalPath;
                    context.Items["RewrittenPath"] = rewrittenPath;

                    debugInfo.AddDetectionStep(LanguageSource.Default, true, true, originalPath, rewrittenPath, "传统URL重写完成");
                }
                else
                {
                    debugInfo.AddWarning($"无需URL重写: {originalPath}");
                }
            }
            catch (Exception ex)
            {
                debugInfo.AddError("PerformUrlRewrite", ex, $"URL重写时发生异常: {context.Request.Path}");
            }
        }

        /// <summary>
        /// 判断是否需要执行URL重写，并计算重写后的路径
        /// 实现需求2.1、5.2: 支持新URL格式的重写逻辑
        /// 更新方法支持新URL格式，确保重写后的路径和语言上下文正确
        /// 例如：/zh-Hans/default.aspx -> /default.aspx
        /// </summary>
        /// <param name="originalPath">原始路径</param>
        /// <param name="languageInfo">语言信息</param>
        /// <param name="rewrittenPath">重写后的路径</param>
        /// <returns>如果需要重写返回true，否则返回false</returns>
        private bool ShouldPerformUrlRewrite(string originalPath, LanguageInfo languageInfo, out string rewrittenPath)
        {
            rewrittenPath = null;

            if (string.IsNullOrEmpty(originalPath) || languageInfo == null || string.IsNullOrEmpty(languageInfo.StandardCode))
                return false;

            try
            {
                // 解析URL格式
                var urlInfo = UrlFormatParser.ParseUrlFormat(originalPath);

                // 记录URL格式解析结果
                var context = HttpContext.Current;
                if (context != null)
                {
                    context.Items["ParsedUrlInfo"] = urlInfo;
                    context.Items["UrlRewriteAttempt"] = true;
                }

                // 处理带语言前缀的URL重写
                if (urlInfo.HasLanguagePrefix)
                {
                    bool result = HandleLanguagePrefixUrlRewrite(originalPath, languageInfo, urlInfo, out rewrittenPath);

                    // 记录语言前缀重写结果
                    if (context != null)
                    {
                        context.Items["LanguagePrefixRewrite"] = result;
                        context.Items["LanguagePrefixRewritePath"] = rewrittenPath;
                    }

                    return result;
                }

                // 处理根路径的特殊情况
                if (urlInfo.Type == UrlFormatType.RootPath || urlInfo.Type == UrlFormatType.RootDomain)
                {
                    bool result = HandleRootPathUrlRewrite(originalPath, languageInfo, urlInfo, out rewrittenPath);

                    // 记录根路径重写结果
                    if (context != null)
                    {
                        context.Items["RootPathRewrite"] = result;
                        context.Items["RootPathRewritePath"] = rewrittenPath;
                    }

                    return result;
                }

                // 处理默认语言简洁URL的特殊情况
                if (urlInfo.Type == UrlFormatType.DefaultLanguageWithoutPrefix &&
                    string.Equals(languageInfo.StandardCode, LanguageConfiguration.DefaultLanguage, StringComparison.OrdinalIgnoreCase))
                {
                    // 默认语言简洁URL通常不需要重写，但需要确保语言上下文正确
                    if (context != null)
                    {
                        context.Items["DefaultLanguageSimpleUrl"] = true;
                        context.Items["NoRewriteNeeded"] = true;
                    }

                    return false; // 不需要重写
                }

                // 其他情况不需要重写
                if (context != null)
                {
                    context.Items["NoRewriteNeeded"] = true;
                    context.Items["RewriteReason"] = "其他情况不需要重写";
                }

                return false;
            }
            catch (Exception ex)
            {
                // 记录异常信息
                var context = HttpContext.Current;
                if (context != null)
                {
                    context.Items["UrlRewriteException"] = ex.Message;
                    context.Items["FallbackToOriginalLogic"] = true;
                }

                // 异常时回退到原有逻辑
                return ShouldPerformUrlRewriteFallback(originalPath, languageInfo, out rewrittenPath);
            }
        }

        /// <summary>
        /// 处理带语言前缀的URL重写
        /// 实现需求2.1: 默认语言带前缀URL的重写逻辑
        /// 处理 /zh-Hans/page.aspx → /page.aspx 的重写，保持语言上下文为 zh-Hans
        /// </summary>
        private bool HandleLanguagePrefixUrlRewrite(string originalPath, LanguageInfo languageInfo, UrlFormatInfo urlInfo, out string rewrittenPath)
        {
            rewrittenPath = null;

            // 检查语言代码是否匹配
            if (!string.Equals(urlInfo.LanguageCode, languageInfo.StandardCode, StringComparison.OrdinalIgnoreCase))
            {
                // 语言代码不匹配，可能需要重定向而不是重写
                return false;
            }

            // 处理默认语言带前缀的URL重写
            if (string.Equals(languageInfo.StandardCode, LanguageConfiguration.DefaultLanguage, StringComparison.OrdinalIgnoreCase))
            {
                // 默认语言：/zh-Hans/page.aspx -> /page.aspx
                // 需求2.1: 保持语言上下文为 zh-Hans，设置正确的原始路径和重写路径信息
                rewrittenPath = string.IsNullOrEmpty(urlInfo.ActualPath) ? "/" : urlInfo.ActualPath;

                // 处理根路径情况: /zh-Hans/ -> /default.aspx
                if (rewrittenPath == "/" || rewrittenPath == "")
                {
                    rewrittenPath = "/default.aspx";
                }

                // 确保重写路径以 / 开头
                if (!rewrittenPath.StartsWith("/"))
                {
                    rewrittenPath = "/" + rewrittenPath;
                }

                // 设置重写路径信息到上下文（供其他组件使用）
                var context = HttpContext.Current;
                if (context != null)
                {
                    context.Items["OriginalLanguagePath"] = originalPath;
                    context.Items["RewrittenPath"] = rewrittenPath;
                    context.Items["LanguagePrefix"] = urlInfo.LanguageCode;
                    context.Items["IsDefaultLanguageRewrite"] = true;
                }

                return true;
            }
            else
            {
                // 非默认语言：/en/page.aspx -> /page.aspx
                rewrittenPath = string.IsNullOrEmpty(urlInfo.ActualPath) ? "/" : urlInfo.ActualPath;

                // 处理根路径情况: /en/ -> /default.aspx
                if (rewrittenPath == "/" || rewrittenPath == "")
                {
                    rewrittenPath = "/default.aspx";
                }

                // 确保重写路径以 / 开头
                if (!rewrittenPath.StartsWith("/"))
                {
                    rewrittenPath = "/" + rewrittenPath;
                }

                // 设置重写路径信息到上下文
                var context = HttpContext.Current;
                if (context != null)
                {
                    context.Items["OriginalLanguagePath"] = originalPath;
                    context.Items["RewrittenPath"] = rewrittenPath;
                    context.Items["LanguagePrefix"] = urlInfo.LanguageCode;
                    context.Items["IsDefaultLanguageRewrite"] = false;
                }

                return true;
            }
        }

        /// <summary>
        /// 处理根路径的URL重写
        /// 实现需求1.1: 根路径的特殊处理逻辑
        /// 处理根域名访问（无路径）的默认语言直接显示，确保根路径访问时页面链接基地址正确
        /// </summary>
        private bool HandleRootPathUrlRewrite(string originalPath, LanguageInfo languageInfo, UrlFormatInfo urlInfo, out string rewrittenPath)
        {
            rewrittenPath = null;

            var context = HttpContext.Current;

            // 处理根路径 "/" 的情况
            if (originalPath == "/" && string.Equals(languageInfo.StandardCode, LanguageConfiguration.DefaultLanguage, StringComparison.OrdinalIgnoreCase))
            {
                // 默认语言的根路径重写到默认页面
                rewrittenPath = "/default.aspx";

                // 设置根路径上下文信息
                if (context != null)
                {
                    context.Items["IsRootPathAccess"] = true;
                    context.Items["OriginalRootPath"] = originalPath;
                    context.Items["RewrittenPath"] = rewrittenPath;
                    context.Items["RootPathLanguage"] = languageInfo.StandardCode;

                    // 确保根路径访问时页面链接基地址正确
                    // 根据设计文档分析，根路径的基地址天然正确，无需特殊处理
                    context.Items["BaseUrlCorrect"] = true;
                }

                return true;
            }

            // 处理根域名访问（无路径或空路径）的情况
            if ((string.IsNullOrEmpty(originalPath) || originalPath == "") &&
                string.Equals(languageInfo.StandardCode, LanguageConfiguration.DefaultLanguage, StringComparison.OrdinalIgnoreCase))
            {
                // 根域名访问，默认语言直接显示
                rewrittenPath = "/default.aspx";

                // 设置根域名访问上下文信息
                if (context != null)
                {
                    context.Items["IsRootDomainAccess"] = true;
                    context.Items["OriginalRootPath"] = originalPath ?? "";
                    context.Items["RewrittenPath"] = rewrittenPath;
                    context.Items["RootDomainLanguage"] = languageInfo.StandardCode;

                    // 实现根路径的语言上下文设置和Cookie管理
                    SetRootPathLanguageContext(context, languageInfo);
                }

                return true;
            }

            // 非默认语言的根路径不在这里处理，应该通过重定向处理
            return false;
        }

        /// <summary>
        /// 设置根路径的语言上下文和Cookie管理
        /// 实现需求1.1: 根路径的语言上下文设置和Cookie管理
        /// </summary>
        private void SetRootPathLanguageContext(HttpContext context, LanguageInfo languageInfo)
        {
            if (context == null || languageInfo == null)
                return;

            try
            {
                // 设置语言上下文
                context.Items["lang"] = languageInfo.StandardCode;
                context.Items["LanguageInfo"] = languageInfo;
                context.Items["RootPathOptimized"] = true;

                // 设置语言Cookie，确保后续访问保持语言偏好
                var langCookie = new HttpCookie("lang", languageInfo.StandardCode)
                {
                    Expires = DateTime.Now.AddYears(1),
                    Path = "/",
                    HttpOnly = false // 允许JavaScript访问，供translate.js使用
                };

                // 检查是否已存在相同的Cookie，避免重复设置
                var existingCookie = context.Request.Cookies["lang"];
                if (existingCookie == null || existingCookie.Value != languageInfo.StandardCode)
                {
                    context.Response.Cookies.Add(langCookie);
                    context.Items["RootPathCookieSet"] = true;
                }

                // 设置调试信息
                var debugInfo = context.Items["LanguageDebugInfo"] as LanguageDebugInfo;
                debugInfo?.AddDetectionStep(LanguageSource.Default, true, true,
                    "根路径", languageInfo.StandardCode, "根路径语言上下文设置完成");
            }
            catch (Exception ex)
            {
                // 记录错误但不影响正常流程
                var debugInfo = context.Items["LanguageDebugInfo"] as LanguageDebugInfo;
                debugInfo?.AddError("SetRootPathLanguageContext", ex, "设置根路径语言上下文时发生异常");
            }
        }

        /// <summary>
        /// 回退到原有的URL重写逻辑（保持兼容性）
        /// </summary>
        private bool ShouldPerformUrlRewriteFallback(string originalPath, LanguageInfo languageInfo, out string rewrittenPath)
        {
            rewrittenPath = null;

            // 检查路径是否以语言代码开头
            string languagePrefix = $"/{languageInfo.StandardCode}/";
            string languagePrefixNoSlash = $"/{languageInfo.StandardCode}";

            if (originalPath.StartsWith(languagePrefix, StringComparison.OrdinalIgnoreCase))
            {
                // 情况1: /zh-Hans/default.aspx -> /default.aspx
                rewrittenPath = "/" + originalPath.Substring(languagePrefix.Length);

                // 处理根路径情况: /zh-Hans/ -> /default.aspx
                if (rewrittenPath == "/")
                {
                    rewrittenPath = "/default.aspx"; // 重写为默认页面
                }

                return true;
            }
            else if (originalPath.Equals(languagePrefixNoSlash, StringComparison.OrdinalIgnoreCase))
            {
                // 情况2: /zh-Hans -> /default.aspx
                rewrittenPath = "/default.aspx";
                return true;
            }

            // 检查是否为其他语言代码的路径（防止误重写）
            if (IsLanguageCodePath(originalPath))
            {
                // 这是其他语言的路径，但当前语言信息不匹配，可能需要重定向而不是重写
                return false;
            }

            return false;
        }

        /// <summary>
        /// 检查路径是否为语言代码路径
        /// </summary>
        /// <param name="path">要检查的路径</param>
        /// <returns>如果是语言代码路径返回true，否则返回false</returns>
        private bool IsLanguageCodePath(string path)
        {
            if (string.IsNullOrEmpty(path))
                return false;

            try
            {
                // 使用UrlService解析路径中的语言信息
                string language, originalLanguagePath, actualPath;
                return UrlService.TryExtractLanguage(path, out language, out originalLanguagePath, out actualPath);
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 处理语言检测错误
        /// 任务7.2: 添加错误处理监控
        /// </summary>
        private void HandleLanguageDetectionError(HttpContext context, Exception ex, LanguageDebugInfo debugInfo)
        {
            try
            {
                // 使用默认语言
                var defaultLanguageInfo = LanguageInfo.CreateDefault();
                SetLanguageContextAndRewritePath(context, defaultLanguageInfo, debugInfo);

                debugInfo.DetectedLanguage = defaultLanguageInfo;
                debugInfo.AddWarning($"语言检测异常，已降级到默认语言: {ex.Message}");
            }
            catch (Exception innerEx)
            {
                debugInfo.AddError("HandleLanguageDetectionError", innerEx, "处理语言检测错误时发生异常");
            }
        }

        /// <summary>
        /// 获取重定向原因描述
        /// </summary>
        private string GetRedirectReason(LanguageInfo languageInfo)
        {
            switch (languageInfo.Source)
            {
                case LanguageSource.UrlPath:
                    return "语言别名或目录路径标准化";
                case LanguageSource.QueryString:
                    return "查询参数语言转换为路径格式";
                case LanguageSource.Cookie:
                    return "Cookie语言偏好应用";
                case LanguageSource.BrowserHeader:
                    return "浏览器语言偏好应用";
                case LanguageSource.Default:
                    return "应用默认语言";
                default:
                    return "语言标准化";
            }
        }

        /// <summary>
        /// 验证重定向URL的有效性
        /// </summary>
        private bool IsValidRedirectUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
                return false;

            // 检查URL长度（防止过长的URL）
            if (url.Length > 2048)
                return false;

            // 检查是否包含危险字符
            if (url.Contains("<") || url.Contains(">") || url.Contains("\"") || url.Contains("'"))
                return false;

            // 检查是否为相对URL（我们只处理相对重定向）
            if (url.StartsWith("http://", StringComparison.OrdinalIgnoreCase) ||
                url.StartsWith("https://", StringComparison.OrdinalIgnoreCase))
                return false;

            return true;
        }

        public void Dispose()
        {
            // 无需释放资源
        }
    }
}