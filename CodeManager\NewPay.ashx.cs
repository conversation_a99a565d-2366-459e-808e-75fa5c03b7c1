using CommonLib;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Web;
using Account.Web.pay;

namespace Account.Web
{
    /// <summary>
    /// 360支付回调处理
    /// </summary>
    public class NewPay : IHttpHandler
    {
        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "application/json";
            
            try
            {
                // 只处理POST请求
                if (context.Request.HttpMethod != "POST")
                {
                    WriteErrorResponse(context, -1, "只支持POST请求");
                    return;
                }

                // 读取请求体
                string requestBody;
                using (var reader = new StreamReader(context.Request.InputStream, Encoding.UTF8))
                {
                    requestBody = reader.ReadToEnd();
                }

                if (string.IsNullOrEmpty(requestBody))
                {
                    WriteErrorResponse(context, -1, "请求体为空");
                    return;
                }

                LogHelper.Log.InfoFormat("360支付回调请求: {0}", requestBody);

                // 解析JSON数据
                var callbackData = JsonConvert.DeserializeObject<Pay360CallbackData>(requestBody);
                if (callbackData == null)
                {
                    WriteErrorResponse(context, -1, "JSON解析失败");
                    return;
                }

                // 验证必要参数
                if (string.IsNullOrEmpty(callbackData.mfr_order_id))
                {
                    WriteErrorResponse(context, -1, "mfr_order_id不能为空");
                    return;
                }

                // 验证签名
                if (!ValidateSign(callbackData))
                {
                    LogHelper.Log.ErrorFormat("360支付回调签名验证失败: {0}", requestBody);
                    WriteErrorResponse(context, -1, "签名验证失败");
                    return;
                }

                // 处理支付成功回调 (支持多种成功状态)
                if (callbackData.order_status == 50 ||  // Success - 交易完成
                    callbackData.order_status == 20 ||  // PaidBeforeNotify - 付款完成（待通知厂商）
                    callbackData.order_status == 30)    // PaidAfterNotify - 待厂商发权益（已通知厂商）
                {
                    ProcessPaymentSuccess(callbackData);
                    WriteSuccessResponse(context);
                }
                else
                {
                    LogHelper.Log.InfoFormat("360支付回调状态非成功: order_status={0}, mfr_order_id={1}", 
                        callbackData.order_status, callbackData.mfr_order_id);
                    WriteSuccessResponse(context); // 仍然返回成功，避免重复推送
                }
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error("360支付回调处理异常", ex);
                WriteErrorResponse(context, -1, "处理异常");
            }
        }

        /// <summary>
        /// 验证360回调签名
        /// </summary>
        private bool ValidateSign(Pay360CallbackData data)
        {
            try
            {
                var appSecret = ConfigurationManager.AppSettings["Pay360AppSecret"];
                if (string.IsNullOrEmpty(appSecret))
                {
                    LogHelper.Log.Error("Pay360AppSecret配置未找到");
                    return false;
                }

                // 构建签名参数字典
                var signParams = new Dictionary<string, object>();
                
                if (!string.IsNullOrEmpty(data.app_id))
                    signParams["app_id"] = data.app_id;
                if (!string.IsNullOrEmpty(data.bank_trade_code))
                    signParams["bank_trade_code"] = data.bank_trade_code;
                if (data.callback_type.HasValue)
                    signParams["callback_type"] = data.callback_type.Value;
                if (data.mfr_order_amount.HasValue)
                    signParams["mfr_order_amount"] = data.mfr_order_amount.Value;
                if (!string.IsNullOrEmpty(data.mfr_order_id))
                    signParams["mfr_order_id"] = data.mfr_order_id;
                if (!string.IsNullOrEmpty(data.mfr_product_id))
                    signParams["mfr_product_id"] = data.mfr_product_id;
                if (!string.IsNullOrEmpty(data.mfr_product_name))
                    signParams["mfr_product_name"] = data.mfr_product_name;
                if (!string.IsNullOrEmpty(data.order_code))
                    signParams["order_code"] = data.order_code;
                if (data.order_status.HasValue)
                    signParams["order_status"] = data.order_status.Value;
                if (data.pay_channel.HasValue)
                    signParams["pay_channel"] = data.pay_channel.Value;
                if (data.qid.HasValue)
                    signParams["qid"] = data.qid.Value;
                if (data.timestamp.HasValue)
                    signParams["timestamp"] = data.timestamp.Value;
                if (!string.IsNullOrEmpty(data.trans_time))
                    signParams["trans_time"] = data.trans_time;

                // 按参数名排序并拼接
                var sortedParams = signParams.OrderBy(p => p.Key);
                var signString = string.Join("&", sortedParams.Select(p => $"{p.Key}={p.Value}")) + appSecret;
                
                LogHelper.Log.InfoFormat("360签名字符串: {0}", signString);

                // MD5加密
                var computedSign = GetMD5Hash(signString);
                
                LogHelper.Log.InfoFormat("360计算签名: {0}, 接收签名: {1}", computedSign, data.sign);

                return string.Equals(computedSign, data.sign, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error("360签名验证异常", ex);
                return false;
            }
        }

        /// <summary>
        /// 处理支付成功
        /// </summary>
        private void ProcessPaymentSuccess(Pay360CallbackData data)
        {
            try
            {
                // 转换支付方式 (360的pay_channel: 1=微信, 2=支付宝)
                int payChannel = data.pay_channel ?? 0;

                // 金额转换：360回调金额单位是分，转换为元
                double amount = 0;
                if (data.mfr_order_amount.HasValue)
                {
                    amount = Math.Round(data.mfr_order_amount.Value / 100.0, 2);
                }

                // 调用360支付处理逻辑
                NewPayUtil.Process360PaymentSuccess(data.mfr_order_id, payChannel, amount);
            }
            catch (Exception ex)
            {
                LogHelper.Log.Error($"360 HTTP回调处理异常: mfr_order_id={data.mfr_order_id}", ex);
                throw;
            }
        }

        /// <summary>
        /// MD5加密
        /// </summary>
        private string GetMD5Hash(string input)
        {
            using (var md5 = MD5.Create())
            {
                var inputBytes = Encoding.UTF8.GetBytes(input);
                var hashBytes = md5.ComputeHash(inputBytes);
                return BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
            }
        }

        /// <summary>
        /// 写入成功响应
        /// </summary>
        private void WriteSuccessResponse(HttpContext context)
        {
            var response = new
            {
                code = 200,
                message = "success"
            };
            context.Response.Write(JsonConvert.SerializeObject(response));
        }

        /// <summary>
        /// 写入错误响应
        /// </summary>
        private void WriteErrorResponse(HttpContext context, int code, string message)
        {
            var response = new
            {
                code = code,
                message = message
            };
            context.Response.Write(JsonConvert.SerializeObject(response));
        }

        public bool IsReusable
        {
            get { return false; }
        }
    }

    /// <summary>
    /// 360支付回调数据模型
    /// </summary>
    public class Pay360CallbackData
    {
        public string app_id { get; set; }
        public string bank_trade_code { get; set; }
        public int? callback_type { get; set; }
        public int? mfr_order_amount { get; set; }
        public string mfr_order_id { get; set; }
        public string mfr_product_id { get; set; }
        public string mfr_product_name { get; set; }
        public string order_code { get; set; }
        public int? order_status { get; set; }
        public int? pay_channel { get; set; }
        public int? qid { get; set; }
        public string sign { get; set; }
        public int? timestamp { get; set; }
        public string trans_time { get; set; }
    }
}